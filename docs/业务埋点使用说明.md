# 基于 bizType 的业务埋点使用说明

## 概述

为了规范业务流程的埋点行为，我们重新设计了业务埋点方法。新的方法基于 `bizType`（业务类型编号）来确定是否进行埋点，通过映射表将 `bizType` 转换为对应的业务编号。不在映射表中的业务默认不进行页面埋点。

## 文件结构

- `src/common/tracking/bizTypeMapping.js` - bizType 到 businessCode 的映射配置
- `src/common/tracking/businessTracking.js` - 埋点事件的具体实现
- `src/common/util.js` - 对外暴露的埋点方法接口

## bizType 映射配置

当前支持的 bizType 映射：

| bizType | businessCode | 业务名称 |
|---------|--------------|----------|
| 014001  | xh           | 销户业务 |

### 添加新业务映射

在 `src/common/tracking/bizTypeMapping.js` 中的 `BIZ_TYPE_MAPPING` 对象中添加新的映射：

```javascript
export const BIZ_TYPE_MAPPING = {
  '014001': 'xh',  // 销户业务
  '010001': 'kh',  // 开户业务（示例）
  // 添加更多业务映射...
};
```

## 引用方式

```javascript
import { trackBusinessPageView, trackBusinessPageClick, trackBusinessError } from '@/common/tracking/businessTracking';
```

## 新增的埋点方法

### 1. trackBusinessEvent (基础方法)

```javascript
import { trackBusinessEvent } from '@/common/tracking/businessTracking';

trackBusinessEvent({
  bizType: '014001',
  eventType: 'view',
  pageIdentifier: 'jsp',
  elementName: '页面标题'
});
```

**参数说明（命名参数对象）：**
- `bizType`: 业务类型编号（如 '014001'）
- `eventType`: 事件类型，支持 'view'、'click'、'error'、'show'
- `pageIdentifier`: 页面标识（如 'jsp'）
- `elementName`: 元素名称（如 'next', 'back', 'error_popup'）
- `moduleName`: 模块名称（可选）
- `remarks`: 备注信息（可选）

**支持的事件类型映射：**
- `view` → `ywbl_view` (页面浏览)
- `click` → `ywbl_click` (页面点击)
- `error` → `error` (报错)
- `show` → `ywbl_show` (曝光弹框)

**页面名称生成规则：**
- 格式：`{businessCode}_{pageIdentifier}`
- 示例：bizType '014001' + pageIdentifier 'jsp' → page_name 'xh_jsp'

### 2. trackBusinessPageView (页面浏览埋点)

```javascript
import { trackBusinessPageView } from '@/common/tracking/businessTracking';

trackBusinessPageView({
  bizType: '014001',
  pageIdentifier: 'jsp'
});
```

**参数说明（命名参数对象）：**

- `bizType`: 业务类型编号（如 '014001'）
- `pageIdentifier`: 页面标识（如 'jsp'）
- `elementName`: 元素名称，默认为'页面标题'（可选）
- `moduleName`: 模块名称（可选）
- `remarks`: 备注信息（可选）

**示例：**

```javascript
// 销户业务介绍页页面浏览
trackBusinessPageView({
  bizType: '014001',
  pageIdentifier: 'jsp'
});
```

### 3. trackBusinessPageClick (页面点击埋点)

```javascript
import { trackBusinessPageClick } from '@/common/tracking/businessTracking';

trackBusinessPageClick({
  bizType: '014001',
  pageIdentifier: 'jsp',
  elementName: 'next',
  moduleName: '操作按钮'
});
```

**参数说明（命名参数对象）：**

- `bizType`: 业务类型编号（如 '014001'）
- `pageIdentifier`: 页面标识（如 'jsp'）
- `elementName`: 元素名称（如 'next', 'back'）
- `moduleName`: 模块名称（可选）
- `remarks`: 备注信息（可选）

**示例：**

```javascript
// 销户业务点击下一步按钮
trackBusinessPageClick({
  bizType: '014001',
  pageIdentifier: 'jsp',
  elementName: 'next',
  moduleName: '操作按钮'
});

// 销户业务点击返回按钮
trackBusinessPageClick({
  bizType: '014001',
  pageIdentifier: 'jsp',
  elementName: 'back',
  moduleName: '操作按钮'
});
```

### 4. trackBusinessError (报错埋点)

```javascript
import { trackBusinessError } from '@/common/tracking/businessTracking';

trackBusinessError({
  bizType: '014001',
  pageIdentifier: 'error_popup',
  errorInfo: '网络请求失败'
});
```

**参数说明（命名参数对象）：**

- `bizType`: 业务类型编号（如 '014001'）
- `pageIdentifier`: 页面标识，默认为 'error_popup'（可选）
- `errorInfo`: 错误信息

**示例：**

```javascript
// 销户业务报错弹窗
trackBusinessError({
  bizType: '014001',
  errorInfo: '网络请求失败'
});
```

## 在Vue组件中的使用

```javascript
import { trackBusinessPageView, trackBusinessPageClick, trackBusinessError } from '@/common/tracking/businessTracking';

export default {
  name: 'XhComponent',
  mounted() {
    // 页面加载时埋点
    this.trackPageView();
  },
  methods: {
    trackPageView() {
      trackBusinessPageView({
        bizType: '014001',
        pageIdentifier: 'jsp'
      });
    },

    handleNextClick() {
      // 点击埋点
      trackBusinessPageClick({
        bizType: '014001',
        pageIdentifier: 'jsp',
        elementName: 'next'
      });

      // 业务逻辑...
    },

    handleError(error) {
      // 错误埋点
      trackBusinessError({
        bizType: '014001',
        pageIdentifier: 'jsp',
        errorInfo: error.message
      });
    }
  }
};
```

## 在Service中的使用

替换现有service文件中的错误埋点：

```javascript
import { trackBusinessError } from '@/common/tracking/businessTracking';

function commService(funcNo, params = {}, options = {}) {
  // ... 其他代码

  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      // 使用新的业务埋点方法
      const bizType = sessionStorage.getItem('bizType');
      if (bizType) {
        trackBusinessError({
          bizType,
          errorInfo: JSON.stringify(err)
        });
      }
      return Promise.reject(err);
    }
  );
}
```

## 埋点文档对应关系

根据提供的埋点文档，各个埋点的对应关系如下：

| 页面 | page name | 事件名称 | event name | 属性名称 | element name | 调用方法 |
|------|-----------|----------|------------|----------|--------------|----------|
| 业务办理流程每个页面 | xh_error_popup | 报错 | error | 报错弹窗 | error_popup | `trackBusinessError('xh', '', errorMsg)` |
| 介绍页 | xh_jsp | 页面浏览 | ywbl_view | 页面标题 | 页面标题 | `trackBusinessPageView('xh', 'jsp')` |
| 介绍页 | xh_jsp | 页面点击 | ywbl_click | 下一步 | next | `trackBusinessPageClick('xh', 'jsp', 'next')` |
| 介绍页 | xh_jsp | 页面点击 | ywbl_click | 返回 | back | `trackBusinessPageClick('xh', 'jsp', 'back')` |

## 业务扩展

当需要支持新的业务时，在 `src/common/tracking/bizTypeMapping.js` 中添加新的映射：

```javascript
export const BIZ_TYPE_MAPPING = {
  '014001': 'xh',  // 销户业务
  '010001': 'kh',  // 开户业务（新增）
  '010002': 'xx',  // 其他业务（新增）
};
```

## 注意事项

1. **动态页面名称**：页面名称格式为 `{businessCode}_{pageIdentifier}`，会自动生成
2. **bizType 映射**：只有在映射表中的 bizType 才会进行埋点
3. **参数过滤**：空值参数会被自动过滤，不会发送到埋点系统
4. **错误处理**：如果传入不支持的事件类型，会在控制台输出警告信息
5. **向后兼容**：新方法不会影响现有的 `trackEvent` 方法的使用
