export default [
  {
    path: '/login',
    name: 'login',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/home',
    name: 'home',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: '业务办理'
    }
  },
  {
    path: '/bizProgress',
    name: 'bizProgress',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: '办理进度查询'
    }
  },
  {
    path: '/bizResult',
    name: 'bizResult',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: '办理进度查询'
    }
  },
  {
    path: '/test',
    name: 'test',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: ''
    }
  },
  {
    path: '/accountRight',
    name: 'accountRight',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: '已开通权限查询'
    }
  },
  {
    path: '/pdfDownload',
    name: 'pdfDownload',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    meta: {
      title: ''
    }
  },
  {
    path: '/businessIntroduce',
    name: 'businessIntroduce',
    component: () =>
      import(
        /* webpackChunkName: "home" */ '@/views/home/<USER>'
      ),
    meta: {
      title: '  '
    }
  },
  {
    path: '/accountPermissionAttempt',
    name: 'accountPermissionAttempt',
    component: () =>
      import(
        /* webpackChunkName: "home" */ '@/views/home/<USER>'
      ),
    meta: {
      title: '  '
    }
  },
  {
    path: '/proofApply',
    name: 'proofApply',
    component: () => import('@/views/electronicStatement/proofApply.vue'),
    meta: {
      title: '  '
    }
  }
];
