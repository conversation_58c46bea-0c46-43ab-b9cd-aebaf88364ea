/**
 * 原生壳子调用H5方法
 * 集合写在这里，供原生调用H5
 */

function callMessageUtil(param, callback) {
  callback = callback || function () {};
  if (sessionStorage.getItem('tksdk_env')) {
    $h.callMessageNative(param, callback);
  } else {
    callback($h.callMessageNative(param));
  }
}

/** 
* @description 回调H5初始化检测相关函数(一般H5模块切换会用)	
* @param {}
*/
export const function50113 = function (paramMap) {
  console.log('callback50113');
  window.viewShowCallBack && window.viewShowCallBack(paramMap);
};

/**
 * 公用
 * 日期控件回调H5
 * 说明：日期控件的调用必须要写在页面UI的事件处理函数中，否则多模块开发原生无法获取到当前激活的模块从而导致无法回调H5
 * @param funcNo 50251
 * @param date String 日期 Y
 * @param selector String H5的元素选择器 N
 */
export const function50251 = function (paramMap) {
  window.pickDateCallBack && window.pickDateCallBack(paramMap);
};

/**
 * 拍照身份证回调
 */
export const function60050 = function (paramMap) {
  console.log('callback60050');
  window.imgCallBack && window.imgCallBack(paramMap);
};

/**
 * 拍照普通照片回调
 */
export const function50274 = function (paramMap) {
  window.imgCallBack && window.imgCallBack(paramMap);
};

/**
 * 银行卡识别回调
 */
export const function60052 = function (paramMap) {
  console.log(paramMap);
  window.bankOcrCallBack && window.bankOcrCallBack(paramMap);
};

/**
 * 双向视频回调
 */
export const function60051 = function (paramMap) {
  window.videoCallBack && window.videoCallBack(paramMap);
};


/**
 * 监听权限获取逻辑
 */
export const function60047 = function (paramMap) {
  window.premissionCallBack && window.premissionCallBack(paramMap);
};

/**
 * android点击back按钮触发事件退出当前activity 重写
 * @param paramMap
 */
export const function50107 = (function (paramMap) {
  return function () {
    console.log('监听返回');
    /* const currentPage = window.$router.currentRoute.name;
    if (currentPage !== 'introduce' && $h.getSession('fromIntroduce') === true) {
      window.$router.replace({
        name: 'introduce',
        query: {
          bizType: $h.getSession('bizType')
        }
      });
    } else {
      $h.callMessageNative({
        funcNo: '50114',
        moduleName: 'open'
      });
    } */
    if ($h.getSession('optType') === '1') {
      let reqParams = {
        funcNo: '60099',
        actionType: '5',
        params: {
          optType: '1',
          isSuccess: '0'
        }
      };
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      $h.callMessageNative({
        funcNo: '50114',
        moduleName: $hvue.customConfig.moduleName
      });
    } else if ($h.getSession('optType') === 'loginSuccess') {
      let reqParams = {
        funcNo: '60099',
        actionType: '5',
        params: {
          optType: '1',
          isSuccess: '1'
        }
      };
      const res = $h.callMessageNative(reqParams);
      console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      $h.callMessageNative({
        funcNo: '50114',
        moduleName: $hvue.customConfig.moduleName
      });
    } else {
      $h.callMessageNative({
        funcNo: '50114',
        moduleName: $hvue.customConfig.moduleName
      });
    }
  };
  // var g = 0;
  // return function (paramMap) {
  //   if (Date.now() - g < 2e3) {
  //     g = 0;
  //     $h.callMessageNative({ funcNo: '50041', key: 'isApp' }, (data) => {
  //       data.results[0].value == '1'
  //         ? $h.callMessageNative({ funcNo: '50105', moduleName: 'open' }) //退出应用
  //         : $h.callMessageNative({ funcNo: '50114', moduleName: 'open' }); //退出sdk
  //     });
  //   } else {
  //     g = Date.now();
  //     $h.callMessageNative({
  //       funcNo: '50106',
  //       content: '再按一次退出流程'
  //     });
  //   }
  // };
})();

/**
 * 活体识别回调
 * @param paramMap {
 * base64
 * }
 */
export const function60055 = function (paramMap) {
  console.log('function60055');
  window.livingRecognitionCallBack &&
    window.livingRecognitionCallBack(paramMap);
};

/**
 * 服务器端活体识别回调
 * @param paramMap {
 * base64
 * }
 */
export const function60060 = function (paramMap) {
  console.log('function60060');
  window.livingRecognitionCallBack &&
    window.livingRecognitionCallBack(paramMap);
};

/**
 * 投教视频观看回调
 * @param paramMap {
 * base64
 * }
 */
export const function60088 = function (paramMap) {
  console.log('function60088');
  window.videoPlayCallBack && window.videoPlayCallBack(paramMap);
};

/**
 * 人脸影像（照片）采集
 * @param paramMap {
 * base64
 * }
 */
export const function60029 = function (paramMap) {
  console.log('function60029');
  window.imgCallBack && window.imgCallBack(paramMap);
};

/**
 * 普通单向视频回调
 * @param paramMap
 */
export const function60031 = function (paramMap) {
  console.log('function60031');
  window.oneVideoCallBack && window.oneVideoCallBack(paramMap);
};

/**
 * 将h5调用的60099的参数返回给h5
 * @param paramMap
 */
export const function60098 = function (paramMap) {
  console.log('function60098');
  window.nativeAppCallback && window.nativeAppCallback(paramMap);
};

/**
 * @description 智能单向视频结果返回
 */
export const function60027 = function (paramMap) {
  console.log('function60027 activate');
  window.intelligentRecognitionCallBack &&
    window.intelligentRecognitionCallBack(paramMap);
};