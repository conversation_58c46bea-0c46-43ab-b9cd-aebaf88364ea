/**
 * 业务埋点事件实现
 * 包含所有埋点相关的核心逻辑
 */

import { trackEvent } from '@/common/util';
import {
  getBusinessCodeByBizType,
  isBizTypeSupported,
  getBizTypeMappingInfo
} from './bizTypeMapping';

/**
 * 事件类型映射
 */
const EVENT_TYPE_MAPPING = {
  // 页面浏览
  view: 'ywbl_view',
  // 页面点击
  click: 'ywbl_click',
  // 报错
  error: 'error',
  // 曝光弹框
  show: 'ywbl_show'
};

/**
 * 业务埋点核心方法
 * @param {object} params - 埋点参数对象
 * @param {string} params.bizType - 业务类型编号（如 '014001'）
 * @param {string} params.eventType - 事件类型（'view', 'click', 'error', 'show'）
 * @param {string} params.pageIdentifier - 页面标识（如 'jsp', 'error_popup'）
 * @param {string} params.elementName - 元素名称（如 'next', 'back', 'error_popup'）
 * @param {string} [params.moduleName] - 模块名称
 * @param {string} [params.remarks] - 备注信息
 */
export const trackBusinessEventByBizType = ({
  bizType,
  eventType,
  pageIdentifier,
  elementName,
  moduleName = '',
  remarks = '',
  ...otherParams
}) => {
  // 检查 bizType 是否支持埋点
  if (!isBizTypeSupported(bizType)) {
    const mappingInfo = getBizTypeMappingInfo(bizType);
    console.log(
      `业务埋点: bizType "${bizType}" 不在映射表中，跳过埋点`,
      mappingInfo
    );
    return;
  }

  // 获取对应的 businessCode
  const businessCode = getBusinessCodeByBizType(bizType);
  if (!businessCode) {
    console.warn(`业务埋点: bizType "${bizType}" 映射失败`);
    return;
  }

  // 获取对应的事件名称
  const eventName = EVENT_TYPE_MAPPING[eventType];
  if (!eventName) {
    console.warn(`业务埋点: 不支持的事件类型 "${eventType}"`);
    return;
  }

  // 动态生成页面名称: {businessCode}_{pageIdentifier}
  const pageName = pageIdentifier ? `${businessCode}_${pageIdentifier}` : '';

  // 构建埋点参数
  const trackParams = {
    event_name: eventName,
    page_name: pageName,
    element_name: elementName,
    module_name: moduleName,
    remarks: remarks,
    ...otherParams
  };

  // 过滤空值参数
  const filteredParams = Object.keys(trackParams).reduce((acc, key) => {
    if (
      trackParams[key] !== '' &&
      trackParams[key] !== null &&
      trackParams[key] !== undefined
    ) {
      acc[key] = trackParams[key];
    }
    return acc;
  }, {});

  // 记录埋点调用日志
  console.log(
    `业务埋点: bizType="${bizType}" -> businessCode="${businessCode}", page_name="${pageName}"`,
    filteredParams
  );

  // 调用基础埋点方法
  trackEvent(filteredParams);
};

/**
 * 业务页面浏览埋点
 * @param {object} params - 埋点参数对象
 * @param {string} params.bizType - 业务类型编号（如 '014001'）
 * @param {string} params.pageIdentifier - 页面标识（如 'jsp'）
 * @param {string} [params.elementName] - 元素名称，默认为'页面标题'
 * @param {string} [params.moduleName] - 模块名称
 * @param {string} [params.remarks] - 备注信息
 */
export const trackBusinessPageViewByBizType = ({
  bizType,
  pageIdentifier,
  elementName = '',
  ...otherParams
}) => {
  trackBusinessEventByBizType({
    bizType,
    eventType: 'view',
    pageIdentifier,
    elementName,
    ...otherParams
  });
};

/**
 * 业务页面点击埋点
 * @param {object} params - 埋点参数对象
 * @param {string} params.bizType - 业务类型编号（如 '014001'）
 * @param {string} params.pageIdentifier - 页面标识（如 'jsp'）
 * @param {string} params.elementName - 元素名称（如 'next', 'back'）
 * @param {string} [params.moduleName] - 模块名称
 * @param {string} [params.remarks] - 备注信息
 */
export const trackBusinessPageClickByBizType = ({
  bizType,
  pageIdentifier,
  elementName,
  ...otherParams
}) => {
  trackBusinessEventByBizType({
    bizType,
    eventType: 'click',
    pageIdentifier,
    elementName,
    ...otherParams
  });
};

/**
 * 业务报错埋点
 * @param {object} params - 埋点参数对象
 * @param {string} params.bizType - 业务类型编号（如 '014001'）
 * @param {string} [params.pageIdentifier] - 页面标识，为空时使用 'error_popup'
 * @param {string} params.errorInfo - 错误信息
 */
export const trackBusinessErrorByBizType = ({
  bizType,
  pageIdentifier = 'error_popup',
  errorInfo,
  ...otherParams
}) => {
  trackBusinessEventByBizType({
    bizType,
    eventType: 'error',
    pageIdentifier,
    elementName: 'error_popup',
    moduleName: '报错提示',
    remarks: errorInfo,
    ...otherParams
  });
};

/**
 * 获取埋点统计信息（用于调试）
 * @returns {object} 统计信息
 */
export const getTrackingStats = () => {
  return {
    supportedEventTypes: Object.keys(EVENT_TYPE_MAPPING),
    eventTypeMapping: EVENT_TYPE_MAPPING,
    timestamp: new Date().toISOString()
  };
};

/**
 * 验证埋点参数
 * @param {string} bizType - 业务类型编号
 * @param {string} eventType - 事件类型
 * @param {string} pageIdentifier - 页面标识
 * @param {string} elementName - 元素名称
 * @returns {object} 验证结果
 */
export const validateTrackingParams = (
  bizType,
  eventType,
  pageIdentifier,
  elementName
) => {
  const issues = [];
  const warnings = [];

  if (!bizType) {
    issues.push('缺少 bizType 参数');
  } else if (!isBizTypeSupported(bizType)) {
    warnings.push(`bizType "${bizType}" 不在映射表中，将跳过埋点`);
  }

  if (!eventType) {
    issues.push('缺少事件类型');
  } else if (!EVENT_TYPE_MAPPING[eventType]) {
    issues.push(`不支持的事件类型 "${eventType}"`);
  }

  if (!pageIdentifier) {
    issues.push('缺少页面标识');
  }

  if (!elementName) {
    warnings.push('缺少元素名称');
  }

  const businessCode = getBusinessCodeByBizType(bizType);
  const generatedPageName =
    pageIdentifier && businessCode ? `${businessCode}_${pageIdentifier}` : '';

  return {
    isValid: issues.length === 0,
    hasWarnings: warnings.length > 0,
    issues,
    warnings,
    bizType,
    businessCode,
    generatedPageName,
    willTrack: issues.length === 0 && isBizTypeSupported(bizType)
  };
};

// 为了方便直接引用，也导出简化名称的方法
export const trackBusinessEvent = trackBusinessEventByBizType;
export const trackBusinessPageView = trackBusinessPageViewByBizType;
export const trackBusinessPageClick = trackBusinessPageClickByBizType;
export const trackBusinessError = trackBusinessErrorByBizType;

// 导出所有方法
export default {
  trackBusinessEvent,
  trackBusinessPageView,
  trackBusinessPageClick,
  trackBusinessError,
  getTrackingStats,
  validateTrackingParams,
  EVENT_TYPE_MAPPING
};
