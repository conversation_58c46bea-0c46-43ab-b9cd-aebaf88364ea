/**
 * bizType 到 businessCode 的映射配置
 * 用于确定哪些业务需要进行埋点
 */

/**
 * bizType 到 businessCode 的映射表
 * key: bizType (业务类型编号)
 * value: businessCode (业务编号，用于生成页面名称)
 */
export const BIZ_TYPE_MAPPING = {
  // 销户业务
  '014001': 'xh'
};

/**
 * 根据 bizType 获取对应的 businessCode
 * @param {string} bizType - 业务类型编号
 * @returns {string|null} businessCode - 业务编号，如果不支持则返回 null
 */
export const getBusinessCodeByBizType = (bizType) => {
  if (!bizType) {
    return null;
  }

  return BIZ_TYPE_MAPPING[bizType] || null;
};

/**
 * 检查指定的 bizType 是否支持埋点
 * @param {string} bizType - 业务类型编号
 * @returns {boolean} 是否支持埋点
 */
export const isBizTypeSupported = (bizType) => {
  return bizType && BIZ_TYPE_MAPPING.hasOwnProperty(bizType);
};

/**
 * 获取所有支持埋点的 bizType 列表
 * @returns {string[]} 支持埋点的 bizType 数组
 */
export const getSupportedBizTypes = () => {
  return Object.keys(BIZ_TYPE_MAPPING);
};

/**
 * 获取所有支持埋点的 businessCode 列表
 * @returns {string[]} 支持埋点的 businessCode 数组
 */
export const getSupportedBusinessCodes = () => {
  return Object.values(BIZ_TYPE_MAPPING);
};

/**
 * 根据 businessCode 反向查找 bizType
 * @param {string} businessCode - 业务编号
 * @returns {string|null} bizType - 业务类型编号，如果不存在则返回 null
 */
export const getBizTypeByBusinessCode = (businessCode) => {
  if (!businessCode) {
    return null;
  }

  const entry = Object.entries(BIZ_TYPE_MAPPING).find(
    ([, code]) => code === businessCode
  );
  return entry ? entry[0] : null;
};

/**
 * 获取业务映射信息（用于调试和日志）
 * @param {string} bizType - 业务类型编号
 * @returns {object} 映射信息
 */
export const getBizTypeMappingInfo = (bizType) => {
  const businessCode = getBusinessCodeByBizType(bizType);
  const isSupported = isBizTypeSupported(bizType);

  return {
    bizType,
    businessCode,
    isSupported,
    mappingExists: businessCode !== null
  };
};

// 导出默认对象，包含所有方法
export default {
  BIZ_TYPE_MAPPING,
  getBusinessCodeByBizType,
  isBizTypeSupported,
  getSupportedBizTypes,
  getSupportedBusinessCodes,
  getBizTypeByBusinessCode,
  getBizTypeMappingInfo
};
