/**
 * 容器埋点工具类
 * 只使用事件总线获取 pageIdentifier，简化逻辑
 */

import { trackBusinessPageClick, trackBusinessPageView } from '@/common/tracking/businessTracking';
import pageIdentifierBus from '@/common/tracking/pageIdentifierBus';

/**
 * 容器埋点工具类
 */
class ContainerTracking {
  /**
   * 容器点击埋点
   * @param {string} elementName - 元素名称（如 'next', 'back'）
   * @param {Object} options - 其他选项
   */
  static trackClick(elementName, options = {}) {
    const bizType = $h.getSession('bizType');
    if (!bizType) {
      console.log('容器埋点: 未找到 bizType，跳过埋点');
      return;
    }

    const pageIdentifier = pageIdentifierBus.getPageIdentifier();
    if (!pageIdentifier) {
      console.log('容器埋点: 未找到 pageIdentifier，跳过埋点');
      return;
    }

    trackBusinessPageClick({
      bizType,
      pageIdentifier,
      elementName,
      moduleName: options.moduleName,
      ...options
    });

    console.log(
      `容器埋点: bizType=${bizType}, pageIdentifier=${pageIdentifier}, elementName=${elementName}`
    );
  }

  /**
   * 下一步按钮埋点
   * @param {Object} options - 其他选项
   */
  static trackNext(options = {}) {
    this.trackClick('next', options);
  }

  /**
   * 返回按钮埋点
   * @param {Object} options - 其他选项
   */
  static trackBack(options = {}) {
    this.trackClick('back', options);
  }

  /**
   * 页面浏览埋点
   * @param {Object} options - 其他选项
   */
  static trackPageView(options = {}) {
    const bizType = $h.getSession('bizType');
    if (!bizType) {
      console.log('容器埋点: 未找到 bizType，跳过页面浏览埋点');
      return;
    }

    const pageIdentifier = pageIdentifierBus.getPageIdentifier();
    if (!pageIdentifier) {
      console.log('容器埋点: 未找到 pageIdentifier，跳过页面浏览埋点');
      return;
    }

    trackBusinessPageView({
      bizType,
      pageIdentifier,
      elementName: options.elementName || pageIdentifier,
      ...options
    });

    console.log(`容器页面浏览埋点: bizType=${bizType}, pageIdentifier=${pageIdentifier}`);
  }
}

export default ContainerTracking;
