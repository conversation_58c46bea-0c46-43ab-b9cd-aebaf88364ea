/**
 * 业务埋点模块入口文件
 *
 * 使用方式：
 * import { trackBusinessPageView, trackBusinessPageClick, trackBusinessError } from '@/common/tracking/businessTracking';
 *
 * 或者：
 * import { trackBusinessPageView, trackBusinessPageClick, trackBusinessError } from '@/common/tracking';
 */

export {
  trackBusinessEventByBizType,
  trackBusinessPageViewByBizType,
  trackBusinessPageClickByBizType,
  trackBusinessErrorByBizType,
  trackBusinessEvent,
  trackBusinessPageView,
  trackBusinessPageClick,
  trackBusinessError,
  getTrackingStats,
  validateTrackingParams
} from './businessTracking';

export {
  BIZ_TYPE_MAPPING,
  getBusinessCodeByBizType,
  isBizTypeSupported,
  getSupportedBizTypes,
  getSupportedBusinessCodes,
  getBizTypeByBusinessCode,
  getBizTypeMappingInfo
} from './bizTypeMapping';
