/**
 * 页面标识事件总线
 * 用于在页面组件和容器之间传递 pageIdentifier
 */

class PageIdentifierBus {
  constructor() {
    this.currentPageIdentifier = null;
    this.listeners = [];
  }

  /**
   * 设置当前页面标识并自动触发页面浏览埋点
   * @param {string} pageIdentifier - 页面标识
   */
  setPageIdentifier(pageIdentifier) {
    this.currentPageIdentifier = pageIdentifier;
    console.log(`页面标识总线: 设置页面标识为 ${pageIdentifier}`);

    // 通知所有监听者
    this.listeners.forEach((callback) => {
      try {
        callback(pageIdentifier);
      } catch (error) {
        console.error('页面标识总线: 监听器执行错误', error);
      }
    });

    // 自动触发页面浏览埋点
    this.triggerPageViewTracking();
  }

  /**
   * 触发页面浏览埋点
   */
  triggerPageViewTracking() {
    // 延迟执行，确保在下一个事件循环中执行
    setTimeout(() => {
      try {
        // 动态导入避免循环依赖
        import('./containerTracking.js').then(
          ({ default: ContainerTracking }) => {
            ContainerTracking.trackPageView();
          }
        );
      } catch (error) {
        console.error('页面标识总线: 触发页面浏览埋点失败', error);
      }
    }, 0);
  }

  /**
   * 获取当前页面标识
   * @returns {string|null} 当前页面标识
   */
  getPageIdentifier() {
    return this.currentPageIdentifier;
  }

  /**
   * 监听页面标识变化
   * @param {function} callback - 回调函数
   */
  onPageIdentifierChange(callback) {
    this.listeners.push(callback);

    // 如果已经有页面标识，立即调用回调
    if (this.currentPageIdentifier) {
      callback(this.currentPageIdentifier);
    }
  }

  /**
   * 移除监听器
   * @param {function} callback - 要移除的回调函数
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 清除所有监听器和当前页面标识
   */
  clear() {
    this.currentPageIdentifier = null;
    this.listeners = [];
  }
}

// 创建全局实例
const pageIdentifierBus = new PageIdentifierBus();

// 在 Vue 原型上添加方法，方便在组件中使用
if (typeof window !== 'undefined' && window.Vue) {
  window.Vue.prototype.$setPageIdentifier = (pageIdentifier) => {
    pageIdentifierBus.setPageIdentifier(pageIdentifier);
  };

  window.Vue.prototype.$getPageIdentifier = () => {
    return pageIdentifierBus.getPageIdentifier();
  };
}

export default pageIdentifierBus;
