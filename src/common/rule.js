export const rules = {
  email: {
    getMessage: () => `邮箱格式不正确`,
    validate: (value) =>
    /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.test(value)
  },
  address: {
    getMessage: () => `联系地址超过最大字符长度60`,
    validate: (value) => (value.length <= 60 ? true : false)
  },
  mobileTel: {
    getMessage: () => `手机号码格式不正确`,
    validate: (value) => /^1[3-9]\d{9}$/.test(value)
  },
  emergency_contact_name: {
    getMessage: () => `紧急联系人姓名格式不正确`,
    validate: (value) =>
      /^[\u4E00-\u9FA5]{2,14}([·•]?[\u4E00-\u9FA5]+)*$/.test(value)
  },
  emergency_contact_phone: {
    getMessage: () => `紧急联系人电话格式不正确`,
    validate: (value) => /1[3-9][\d]{9}/.test(value)
  },
  zipcode: {
    getMessage: () => `邮政编码格式不正确`,
    validate: (value) => /\d{6}/.test(value)
  },
  id_address: {
    getMessage: () => `身份证地址格式不正确`,
    validate: (value) =>
      /^[\u4E00-\u9FA5\w\d\-\s/(),，（）#]{8,32}$/.test(value)
  },
  id_no: {
    getMessage: () => `证件号码（身份证）格式不正确`,
    validate: (value) => /^([\d]{17}[\dXx]|[\d]{15})$/.test(value)
  },
  issued_depart: {
    getMessage: () => `签发机关（发证机关）格式不正确`,
    validate: (value) => /^[\u4E00-\u9FA5\w\d\-\s/]{4,32}$/.test(value)
  },
  relation_name: {
    getMessage: () => `关联人姓名格式不正确`,
    validate: (value) => /[\u4e00-\u9fa5]+$/.test(value)
  },
  relation_id_no: {
    getMessage: () => `证件号码格式不正确`,
    validate: (value) =>
      /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(
        value
      )
  },
  relation_holder: {
    getMessage: () => `关联人股东账号格式不正确`,
    validate: (value) => /[0-9]{10}$|^A[0-9]{9}$/.test(value)
  },
  relation_open_company: {
    getMessage: () => `关联人开户证券公司格式不正确`,
    validate: (value) => /[\u4e00-\u9fa5]+$/.test(value) && value.length >= 2
  },
  holder_share_id: {
    getMessage: () => `持股代码格式不正确`,
    validate: (value) => /[0-9]{6}$/.test(value)
  },
  holder_share_name: {
    getMessage: () => `股票名称格式不正确`,
    validate: (value) => /[\u4e00-\u9fa5]|[a-zA-Z]+$/.test(value)
  },
  holder_share_no: {
    getMessage: () => `持股数量格式不正确`,
    validate: (value) => /^[1-9]\d{0,9}$/.test(value)
  },
  holder_share_ratio: {
    getMessage: () => `持股比例格式不正确`,
    validate: (value) =>
      /^0\.[0]{2}[1-9]\d{0,1}$|^0\.[0][1-9]\d{0,2}$|^0\.[1-9]\d{0,3}$|^0\.[0-9]{0,3}[1-9]$|^[1-9](\.\d{1,4})?$|^[1-9]\d(\.\d{1,4})?$/.test(
        value
      )
  },
  limitsell_share_id: {
    getMessage: () => `持股代码格式不正确`,
    validate: (value) => /[0-9]{6}$/.test(value)
  },
  limitsell_share_name: {
    getMessage: () => `股票名称格式不正确`,
    validate: (value) => /[\u4e00-\u9fa5]|[a-zA-Z]+$/.test(value)
  },
  limitsell_unlock_no: {
    getMessage: () => `解禁数量格式不正确`,
    validate: (value) => /^[1-9]\d{0,9}$/.test(value)
  },
  limitsell_limit_no: {
    getMessage: () => `未解禁数量格式不正确`,
    validate: (value) => /^[1-9]\d{0,9}$/.test(value)
  },
  exchange_type: {
    getMessage: (e) => {
      return '';
    },
    validate: (value) => {
      return true;
    }
  },
  stock_account: {
    getMessage: (a) => {
      return '证券账号格式不正确';
    },
    validate: (value) => /^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value)
  },
  stock_code: {
    getMessage: () => `证券代码格式不正确`,
    validate: (value) => /^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value)
  },
  remark: {
    getMessage: () => ``,
    validate: (value) => true
  },
  client_address: {
    getMessage: () => '详细地址格式不正确',
    validate: (value) => {
      let regExp =
        /[`~!@#$^&*()=|{}':;',\[\].<>?~！@#￥……&*——|{}【】‘；：”“'。，、？%+_]/g;
      let reg = /^(?=.*?[0-9]).*$/;
      let reg1 =
        /^(?=.*?['零','一','二','三','四','五','六','七','八','九','十']).*$/;
      let reg2 = /(镇|组|街|乡|弄|路|区|座|层|号|排|栋|幢|巷|村|队|室)/;
      let reg3 = /^(?=.*?['省','市','自治区','特别行政区']).*$/;
      let reg4 = /(\w)\1{4,}/g;
      let reg5 = /([\u4E00-\uFA20])\1{4,}/g;
      if (value.indexOf('海外') === 0) return true;
      if (regExp.test(value)) {
        return false;
      }
      if (
        value.length < 5 ||
        value.length > 120 ||
        !(reg.test(value) || reg1.test(value)) ||
        !reg2.test(value) ||
        reg4.test(value) ||
        reg5.test(value) ||
        reg3.test(value.charAt(value.length - 1))
      ) {
        return false;
      }
      return true;
    }
  },
  client_address: {
    getMessage: () => '详细地址格式不正确',
    validate: (value) => {
      let regExp =
        /[`~!@#$^&*()=|{}':;',\[\].<>?~！@#￥……&*——|{}【】‘；：”“'。，、？%+_]/g;
      let reg = /^(?=.*?[0-9]).*$/;
      let reg1 =
        /^(?=.*?['零','一','二','三','四','五','六','七','八','九','十']).*$/;
      /* let reg2 =
        /(小区|楼|村|巷|胡同|里弄|号|大厦|栋|幢|室|元|门|组|房|座|层排)/;
      let reg3 =
        /^(?=.*?['省','市','县','旗','镇','州','盟','乡','所','木','村','街道','街','路','区','巷','无']).*$/; */
      let reg2 = /(自治区|省|市|特别行政区)/;
      let reg3 = /(组|座|层|号|栋|幢|室|厦|队)/;
      let reg4 = /(\w)\1{4,}/g;
      let reg5 = /([\u4E00-\uFA20])\1{4,}/g;
      if (value.indexOf('海外') === 0) return true;
      if (regExp.test(value)) {
        return false;
      }
      if (
        value.length < 5 ||
        value.length > 120 ||
        !(reg.test(value) || reg1.test(value)) ||
        !reg2.test(value) ||
        !reg3.test(value) ||
        reg4.test(value) ||
        reg5.test(value)
      ) {
        return false;
      }
      return true;
    }
  }
};
