/**
 * 基于 bizType 的业务埋点方法测试
 */

// Mock window.collectEvent
global.window = {
  collectEvent: jest.fn()
};

// Mock console.log and console.warn
global.console = {
  log: jest.fn(),
  warn: jest.fn()
};

import { trackBusinessEvent, trackBusinessPageView, trackBusinessPageClick, trackBusinessError } from '../tracking/businessTracking';
import { isBizTypeSupported, getBusinessCodeByBizType } from '../tracking/bizTypeMapping';

describe('基于 bizType 的业务埋点方法测试', () => {
  beforeEach(() => {
    // 清除所有mock调用记录
    window.collectEvent.mockClear();
    console.log.mockClear();
    console.warn.mockClear();
  });

  describe('bizType 映射测试', () => {
    test('应该正确识别支持的 bizType', () => {
      expect(isBizTypeSupported('014001')).toBe(true);
      expect(isBizTypeSupported('999999')).toBe(false);
    });

    test('应该正确映射 bizType 到 businessCode', () => {
      expect(getBusinessCodeByBizType('014001')).toBe('xh');
      expect(getBusinessCodeByBizType('999999')).toBe(null);
    });
  });

  describe('trackBusinessEvent 基础方法测试', () => {
    test('应该正确处理销户业务页面浏览事件', () => {
      trackBusinessEvent({
        bizType: '014001',
        eventType: 'view',
        pageIdentifier: 'jsp',
        elementName: '页面标题'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_jsp',
        element_name: '页面标题'
      });
    });

    test('应该正确处理销户业务页面点击事件', () => {
      trackBusinessEvent({
        bizType: '014001',
        eventType: 'click',
        pageIdentifier: 'jsp',
        elementName: 'next',
        moduleName: '操作按钮'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_click', {
        page_name: 'xh_jsp',
        element_name: 'next',
        module_name: '操作按钮'
      });
    });

    test('应该正确处理销户业务报错事件', () => {
      trackBusinessEvent({
        bizType: '014001',
        eventType: 'error',
        pageIdentifier: 'error_popup',
        elementName: 'error_popup',
        moduleName: '报错提示',
        remarks: '网络请求失败'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('error', {
        page_name: 'xh_error_popup',
        element_name: 'error_popup',
        module_name: '报错提示',
        remarks: '网络请求失败'
      });
    });

    test('应该过滤空值参数', () => {
      trackBusinessEvent({
        bizType: '014001',
        eventType: 'view',
        pageIdentifier: 'jsp',
        elementName: '页面标题',
        moduleName: '',
        remarks: null
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_jsp',
        element_name: '页面标题'
      });
    });

    test('应该对不支持的 bizType 输出日志', () => {
      trackBusinessEvent({
        bizType: '999999',
        eventType: 'view',
        pageIdentifier: 'jsp',
        elementName: '页面标题'
      });

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('业务埋点: bizType "999999" 不在映射表中，跳过埋点'),
        expect.any(Object)
      );
      expect(window.collectEvent).not.toHaveBeenCalled();
    });

    test('应该对不支持的事件类型发出警告', () => {
      trackBusinessEvent({
        bizType: '014001',
        eventType: 'invalid_type',
        pageIdentifier: 'jsp',
        elementName: '页面标题'
      });

      expect(console.warn).toHaveBeenCalledWith('业务埋点: 不支持的事件类型 "invalid_type"');
      expect(window.collectEvent).not.toHaveBeenCalled();
    });
  });

  describe('trackBusinessPageView 页面浏览埋点测试', () => {
    test('应该正确调用销户业务页面浏览埋点', () => {
      trackBusinessPageView({
        bizType: '014001',
        pageIdentifier: 'jsp'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_jsp',
        element_name: '页面标题'
      });
    });

    test('应该使用默认的element_name', () => {
      trackBusinessPageView({
        bizType: '014001',
        pageIdentifier: 'jsp'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_jsp',
        element_name: '页面标题'
      });
    });

    test('应该跳过不支持的 bizType', () => {
      trackBusinessPageView({
        bizType: '999999',
        pageIdentifier: 'jsp'
      });

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('业务埋点: bizType "999999" 不在映射表中，跳过埋点'),
        expect.any(Object)
      );
      expect(window.collectEvent).not.toHaveBeenCalled();
    });
  });

  describe('trackBusinessPageClick 页面点击埋点测试', () => {
    test('应该正确调用销户业务页面点击埋点', () => {
      trackBusinessPageClick({
        bizType: '014001',
        pageIdentifier: 'jsp',
        elementName: 'next',
        moduleName: '操作按钮'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_click', {
        page_name: 'xh_jsp',
        element_name: 'next',
        module_name: '操作按钮'
      });
    });

    test('应该处理返回按钮点击', () => {
      trackBusinessPageClick({
        bizType: '014001',
        pageIdentifier: 'jsp',
        elementName: 'back'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_click', {
        page_name: 'xh_jsp',
        element_name: 'back'
      });
    });
  });

  describe('trackBusinessError 报错埋点测试', () => {
    test('应该正确调用销户业务报错埋点', () => {
      trackBusinessError({
        bizType: '014001',
        pageIdentifier: 'jsp',
        errorInfo: '网络请求失败'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('error', {
        page_name: 'xh_jsp',
        element_name: 'error_popup',
        module_name: '报错提示',
        remarks: '网络请求失败'
      });
    });

    test('应该使用默认页面标识当pageIdentifier为空时', () => {
      trackBusinessError({
        bizType: '014001',
        errorInfo: '系统错误'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('error', {
        page_name: 'xh_error_popup',
        element_name: 'error_popup',
        module_name: '报错提示',
        remarks: '系统错误'
      });
    });
  });

  describe('参数处理测试', () => {
    test('应该正确生成动态页面名称', () => {
      trackBusinessPageView({
        bizType: '014001',
        pageIdentifier: 'test_page'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_test_page',
        element_name: '页面标题'
      });
    });

    test('应该处理自定义参数', () => {
      trackBusinessEvent({
        bizType: '014001',
        eventType: 'view',
        pageIdentifier: 'jsp',
        elementName: '页面标题',
        customParam1: 'value1',
        customParam2: 'value2'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_jsp',
        element_name: '页面标题',
        customParam1: 'value1',
        customParam2: 'value2'
      });
    });

    test('应该支持不同的 bizType 格式', () => {
      // 测试其他 bizType（不在映射表中）
      trackBusinessEvent('888888', 'view', 'test', '测试');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('业务埋点: bizType "888888" 不在映射表中，跳过埋点'),
        expect.any(Object)
      );
      expect(window.collectEvent).not.toHaveBeenCalled();
    });

    test('应该正确处理 bizType 到 businessCode 的映射', () => {
      // 测试销户业务的映射
      trackBusinessPageView({
        bizType: '014001',
        pageIdentifier: 'confirm'
      });

      expect(window.collectEvent).toHaveBeenCalledWith('ywbl_view', {
        page_name: 'xh_confirm',
        element_name: '页面标题'
      });
    });
  });
});
