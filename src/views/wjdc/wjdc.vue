<template>
  <section class="main fixed white_bg" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">问卷调查</h1>
      </div>
    </header>
    <article class="content">
      <div class="wj_container">
        <div
          class="item_container"
          v-for="(item, idx) in wjList"
          :key="idx"
          @click="goToWjSwDetail(item.url)"
        >
          <div>{{ item.name }}</div>
          <img src="@/assets/images/arrow01.png" class="wj_icon" />
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import { exitApp } from '@/common/util';
import { configMap } from '@/service/service';
import { checkedUserAgent } from '@/common/util';
import {
  callPushHM
} from '@/nativeShell/hmosCallH5';
export default {
  data() {
    return {
      wjList: []
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.getWjList();
        }
      },
      immediate: true
    }
  },
  created() {
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    // this.getWjList();
  },
  methods: {
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    viewShowCallBack() {
      this.getWjList();
    },
    getWjList() {
      configMap({
        configKey: 'bc.business.questionnaire'
      }).then((res) => {
        if (
          res.code == 0 &&
          res.data['bc.business.questionnaire'].status == 0
        ) {
          this.wjList = JSON.parse(
            res.data['bc.business.questionnaire'].configValue
          );
        }
      });
    },
    goToWjSwDetail(wjUrl) {
      if (!wjUrl.includes('bc-h5-view')) {
        let isHarmony = checkedUserAgent().harmony;
        wjUrl =
        `${$hvue.customConfig.targetUrl}/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=goSiteForWeb&needToken=1${isHarmony ? '&app_id=yjb3.0' : ''}&backUrl=${encodeURIComponent(wjUrl)}`;
        if(isHarmony) {
          callPushHM(wjUrl)
        } else if ($hvue.platform == 0) {
          window.location.href = wjUrl;
        } else {
          let reqParams = {
            funcNo: '60099',
            moduleName: $hvue.customConfig.moduleName,
            actionType: '6',
            params: {
              url: wjUrl,
              leftType: 1,
              rightType: 99,
              rightText: '',
              enableSwipeLeftGoBack: 0
            }
          };
          console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        }
      }
      this.$router.push({
        name: wjUrl.split('views/')[1]
      });
    }
  }
};
</script>

<style scoped>
.content {
  background-color: #f9f9f9;
}
.wj_container {
  background-color: #fff;
  margin-top: 0.1rem;
  font-size: 0.17rem;
}
.item_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0.1rem;
  border-bottom: 0.005rem solid #eceaea;
  padding: 0.1rem 0;
}
.wj_icon {
  width: 0.15rem;
  height: 0.15rem;
}
.item_container:last-child {
  border-bottom: none;
}
</style>
