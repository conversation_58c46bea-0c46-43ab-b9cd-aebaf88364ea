<template>
  <section v-if="!loading" class="main fixed white_bg" data-page="home">
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon fail"></div>
          <h5>当前链接已失效，请登录佣金宝APP重新申请。</h5>
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import axios from 'axios';
import dayjs from 'dayjs';
export default {
  data() {
    return {
      loading: true
    };
  },
  created() {
    const _this = this;
    const token = this.$route.query.token;
    const http = axios.create({
      baseURL: `${$hvue.customConfig.serverUrl}/download/pdf?token=${token}`,
      timeout: 30000,
      responseType: 'blob'
    });
    http
      .get()
      .then((data) => {
        if (data.status === 200) {
          const fileReader = new FileReader();
          fileReader.readAsText(data.data);
          fileReader.onload = function () {
            try {
              const result = JSON.parse(this.result);
              if (result && result.code === 1000) {
                _this.loading = false;
              } else {
                _hvueToast({
                  mes: result.msg
                });
              }
            } catch (e) {
              const url = window.URL.createObjectURL(new Blob([data.data]));
              const link = document.createElement('a');

              // 判断文件类型
              let fileName = dayjs().format('YYYYMMDD');
              let fileExtension = '.pdf'; // 默认为PDF

              // 通过响应头判断文件类型
              const contentType = data.headers['content-type'];
              if (contentType) {
                if (contentType.includes('application/pdf')) {
                  fileExtension = '.pdf';
                } else {
                  fileExtension = '.xlsx';
                }
              }
              link.href = url;
              link.setAttribute('download', `${fileName}${fileExtension}`);
              document.body.appendChild(link);
              link.click();
            }
          };
        } else {
          return Promise.reject(data.msg);
        }
      })
      .catch((err) => {
        _hvueToast({ mes: err });
        this.loading = false;
      });
  }
};
</script>
