<template>
  <section class="main fixed">
    <t-header title="对账单/资产证明" @back="back"></t-header>
    <article class="content">
      <div class="com_title">
        <h5>请选择您想要开通的证明类型</h5>
      </div>
      <ul class="type_navlist">
        <li
          v-for="({ bizType, desc, title }, k) in viewList"
          :key="k"
          @click="select(bizType)"
        >
          <h5>{{ title }}</h5>
          <p>{{ desc }}</p>
        </li>
      </ul>
    </article>
  </section>
</template>

<script>
import { querySysConfig } from '@/service/service';
import { DICT_TYPE } from '@/common/enumeration';
import { exitApp } from '@/common/util';

export default {
  data() {
    return {
      viewList: []
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const configKey = DICT_TYPE.APPLICATION_TYPE_CONFIG;
      querySysConfig({ configKey })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            try {
              this.viewList = JSON.parse(data[configKey].configValue);
            } catch (error) {
              console.error(error);
              this.viewList = [];
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            mes: err
          });
        });
    },
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.back();
        }
      }
    },
    select(bizType) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, {
          bizType,
          initJumpMode: '0'
        });
      });
    }
  }
};
</script>
