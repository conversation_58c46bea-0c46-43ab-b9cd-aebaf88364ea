<!--
 * @Author: chenjm
 * @Date: 2022-07-14 16:45:57
 * @LastEditors: chenjm
 * @LastEditTime: 2022-10-20 13:49:47
 * @Description: 表单生成容器
-->
<template>
  <section class="main fixed" :class="{ white_bg: isWhiteBg }">
    <t-header
      v-if="displayHeader"
      :title="flowOutputInfo.stepName"
      :show-home-btn="false"
      :show-back="headerBtn.display"
      @back="back"
      @toIndex="toIndex"
    ></t-header>
    <article class="content">
      <FormilyParserH5
        ref="formParser"
        :schema="schema"
        :flow-data="flowOutputInfo"
        @onFormMount="onFormMount"
        @onFormDataInit="onFormDataInit"
        @onFieldValueChange="onFieldValueChange"
        @fieldCustomChange="fieldCustomChange"
      />
    </article>
    <t-footer
      v-show="footerBtn.display"
      :button-txt="footerBtn.text"
      :disabled="footerBtn.status === 0"
      @triggerEvent="triggerEvent"
    ></t-footer>
    <rejectResult
      v-if="showRejectResult"
      ref="rejectResult"
      :reject-list="rejectList"
      @confirm="rejectConfirm"
      @back="back"
      @toIndex="toIndex"
    />
  </section>
</template>

<script>
import '@/components/register/index.js';
import flowMixin, { initFlow } from '@/common/flowMixinV2';
import { baseService } from '@/service/baseService';
import { EVENT_NAME } from '@/common/formEnum';
import {
  underlineToCamel,
  trackEvent,
  uniqueObjectsArray
} from '@/common/util';
import { mapGetters } from 'vuex';
import rejectResult from './components/rejectResult.vue';
import ContainerTracking from '@/common/tracking/containerTracking';

export default {
  name: 'Container',
  components: {
    rejectResult
  },
  mixins: [flowMixin],
  provide() {
    return {
      tkFlowInfo: this.getTKFlowInfo
    };
  },
  data() {
    return {
      initData: {},
      rejectList: [],
      showRejectResult: false,
      rejectListOn: true, // 驳回列表信息展示
      baseService, // 提供给脚本运行使用
      footerBtn: {
        display: false,
        text: '',
        status: 1, //0按钮不可用 1提交表单 2自定义按钮事件
        triggerEvent: null
      },
      headerBtn: {
        display: true,
        event: null
      },
      displayHeader: true,
      schema: '',
      // 一级页面跳转二级页面上下文标识
      subPageCompId: ''
    };
  },
  computed: {
    ...mapGetters('pageContext', [
      'compontId',
      'formData',
      'saveContext',
      'saveContextId',
      'targetPage',
      'targetRefPageContent',
      'targetRefPageTitle',
      'targetRefPageTemplate'
    ]),
    showBackAlert() {
      const pageConfigList = this.flowOutputInfo?.stepContent?.pageConfig;
      if (pageConfigList?.length > 0) {
        return pageConfigList.some(({ propKey, propValue }) => {
          return propKey && propValue === true && propKey === 'backAlert';
        });
      } else {
        return false;
      }
    },
    isWhiteBg() {
      return this.$store.state.flow.isWhiteBg;
    }
  },
  methods: {
    rejectConfirm() {
      try {
        this.schema = JSON.stringify({
          schema: { ...this.flowOutputInfo.stepContent.jsonSchema }
        });
        this.footerBtn.display = true;
        this.setCompValues();
      } catch (e) {
        _hvueToast({ mes: e });
      }
      trackEvent({
        event_name: 'ywbl_view',
        // page_name: this.flowNodeNo.split('-')[1],
        page_name: this.flowOutputInfo.stepName,
        business_name: this.flowOutputInfo.bizName,
        flow_no: this.flowNodeNo.split('-')[1],
        module_name: '页面展示',
        element_name: 'init'
      });
    },

    renderingView() {
      console.log('~~~~~~renderingView~~~~~~~');
      this.rejectList = this.flowOutputInfo?.rejectFields || [];
      const bizType = this.flowOutputInfo.bizType;
      if (this.rejectList.length > 0) {
        this.rejectList = uniqueObjectsArray(this.rejectList);
        console.log('showRejectResult === ', $h.getSession('showRejectResult'));
        console.log(
          'showProfRejectResult === ',
          $h.getSession('showProfRejectResult')
        );
        let showRejectResultFlag = $h.getSession('showRejectResult') === null;
        // 专业投资者业务准入条件节点使用了另外的容器，需要增加额外判断条件
        if (['010042'].includes(bizType)) {
          showRejectResultFlag =
            showRejectResultFlag &&
            $h.getSession('showProfRejectResult') === null;
        }
        if (showRejectResultFlag) {
          $h.setSession('showRejectResult', true);
        } else {
          $h.setSession('showRejectResult', false);
        }
        this.showRejectResult = true;
        this.$nextTick(() => {
          this.$refs.rejectResult.show();
        });
        // 审核驳回的拦截页面显示
        if ($h.getSession('showRejectResult')) {
          return;
        }
      }
      $h.setSession('showRejectResult', false);
      try {
        this.schema = JSON.stringify({
          schema: { ...this.flowOutputInfo.stepContent.jsonSchema }
        });
        this.displayHeader = true;
        this.footerBtn.display = true;
        this.setCompValues();
      } catch (e) {
        _hvueToast({ mes: e });
      }
      trackEvent({
        event_name: 'ywbl_view',
        page_name: this.flowOutputInfo.stepName,
        business_name: this.flowOutputInfo.bizName,
        flow_no: this.flowNodeNo.split('-')[1],
        module_name: '页面展示',
        element_name: 'init'
      });
    },
    // 二级部件返回,设置主部件value值
    setCompValues() {
      if (this.subPageCompId === this.saveContextId) {
        const outProperty = JSON.stringify(this.formData.outProperty);
        this.$refs.formParser.setWidgetValue(
          this.compontId,
          this.saveContext,
          outProperty
        );
        this.$store.commit('pageContext/setSubpageData', false);
      } else {
        this.$refs.formParser?.refresh(); // 刷新表单数据,如果表单不存在,默认不执行
      }
    },
    onFormMount() {
      let { validation } = this.flowOutputInfo.stepContent;
      if (validation?.length > 0) {
        this.footerBtn.triggerEvent = validation;
      }
    },
    onFormDataInit(form) {
      console.log('onFormDataInit start ~');
      // 确保Object key为驼峰命名格式
      Object.keys(form.values).forEach((key) => {
        const isUnderline = key.match(/_/g); // 是否下划线
        if (isUnderline) {
          this.initData[underlineToCamel(key)] = form.values[key];
        } else {
          this.initData[key] = form.values[key];
        }
      });
    },
    triggerEvent() {
      // this.$router.go(0);
      // return;
      const { status, triggerEvent } = this.footerBtn;
      if (status === 0) {
        return;
      } else if (status === 2) {
        triggerEvent();
        return;
      }
      const form = this.$refs.formParser?.form;
      const getFormValues = this.$refs.formParser?.getFormValues;
      const getFormChangeValidate =
        this.$refs.formParser?.getFormChangeValidate;
      let formValues = getFormValues();
      if (form) {
        form
          .validate()
          .then(() => {
            if (getFormChangeValidate().length > 0) {
              _hvueToast({
                mes: getFormChangeValidate()[0].errorMessage
              });
              return;
            }
            if (triggerEvent?.length > 0) {
              let a = eval(triggerEvent);
              a.call(this);
              return;
            }
            this.nextFlow(formValues);
          })
          .catch((errList) => {
            console.error(errList);
          });
      } else {
        console.error('form不存在');
      }
    },
    onFieldValueChange(val) {
      console.log(val);
    },
    /**
     * @description: 自定义控件触发事件
     * @param {Object} eventName
     * @param {Object} eventData
     * @return {*}
     */
    fieldCustomChange({ eventName, eventData = {} }) {
      console.log(eventName, '------fieldCustomChange------');
      switch (eventName) {
        case EVENT_NAME.CLOSE_REJECT:
          this.rejectList = [];
          break;
        case EVENT_NAME.NEXT_STEP:
          // 下一步按钮埋点
          ContainerTracking.trackNext();
          this.nextFlow(eventData, '', (data) => {
            const tkFlowCallback = window.tkFlowNextCallback;
            if (tkFlowCallback) {
              window.tkFlowNextCallback = undefined;
              tkFlowCallback(data);
            }
          });
          break;
        case EVENT_NAME.PREV_FLOW:
          this.prevFlow();
          break;
        case EVENT_NAME.NEXT_BTN:
          this.changeBtnState(eventData);
          break;
        case EVENT_NAME.BACK_BTN:
          this.headerBtn.display = eventData.display;
          this.headerBtn.event = eventData.event;
          break;
        case EVENT_NAME.INDEX_BTN:
          this.changeBtnState({
            text: eventData.text || '返回首页',
            data: () => this.toIndex()
          });
          break;
        case EVENT_NAME.TO_INDEX:
          if (this.showBackAlert) {
            this.$TAlert({
              // title: '温馨提示',
              tips: '请确认是否退出办理流程',
              hasCancel: true,
              confirmBtn: '确认',
              confirm: () => {
                this.toIndex();
              }
            });
          } else {
            this.toIndex();
          }
          break;
        case EVENT_NAME.TO_BIZ_TYPE:
          initFlow({ bizType: eventData.bizType });
          break;
        case EVENT_NAME.DISPLAY_HEADER:
          this.displayHeader = eventData.display;
          break;
        case EVENT_NAME.$JUMP_PAGE:
          this.toSubpage(eventData);
          break;
      }
    },
    changeBtnState({ text = '', display = true, data, btnStatus = 1 }) {
      this.footerBtn.status = btnStatus;
      this.footerBtn.display = display;
      this.footerBtn.text = text;
      if (btnStatus === 2) {
        this.footerBtn.triggerEvent = data;
      }
    },
    /**
     * @description: 跳转至二级部件
     * @arguments {
     "$compontId"  : xxx //部件id
     “$targetPage”: “risk”, //目标页面，业务负责添加
     "saveContextId": '', // 二级部件上下文标识,部件id+随机数
     “$saveContext”: key, // 存放的上下文，业务负责配置
     "$formData":{}//流程引擎返回的表单数据
     “$targetRefPageContent”: “大JSON”,// 关联目标页面JSON，容器负责添加
     "$targetRefPageTitle": '', //目标页面标题
     “$saveContextId”: “:xxxxxxxxxxx”,// 存放的上下文，容器负责添加
     }
     */
    toSubpage(data) {
      const saveContextId = `${data.$compontId}${Math.round(
        Math.random() * 100000
      )}`;
      this.subPageCompId = saveContextId;
      this.$store.commit('pageContext/setSubpageData', {
        ...data,
        saveContextId
      });
      let extendParam;
      if (data.extendParam) {
        extendParam = data.extendParam;
      }
      this.$router.push({
        path: `/form/subpage/${data.$targetPage}`,
        query: extendParam
      });
    },
    back() {
      // 返回按钮埋点
      ContainerTracking.trackBack();

      if (this.headerBtn.event?.constructor.name === 'Function') {
        this.headerBtn.event();
      } else {
        if ($h.getSession('channelType') === '2005000000000') {
          if (this.flowOutputInfo.bizType === '010013') {
            this.$TAlert({
              // title: '温馨提示',
              tips: '请点击左上角“关闭”退出本业务',
              hasCancel: true,
              confirmBtn: '确认',
              confirm: () => {}
            });
            return;
          }
        }
        if (this.showBackAlert) {
          this.$TAlert({
            // title: '温馨提示',
            tips: '请确认是否退出办理流程',
            hasCancel: true,
            confirmBtn: '确认',
            confirm: () => {
              this.toIndex();
            }
          });
        } else {
          this.prevFlow();
        }
      }
    },
    getTKFlowInfo() {
      const { flowNodeNo, inProperty, outProperty } = this.flowOutputInfo;
      return {
        // flowNodeNo,
        // inProperty,
        // outProperty
        ...this.flowOutputInfo
      };
    }
  }
};
</script>
