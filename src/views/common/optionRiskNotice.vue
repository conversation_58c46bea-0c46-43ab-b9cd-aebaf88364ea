<template>
  <section class="main fixed white_bg" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">期权交易法律与风险告知书</h1>
      </div>
    </header>
    <article class="content">
      <div class="notice_container">
        <div class="notice_title">尊敬的投资者/参与者：</div>
        <div class="notice_intro">
          感谢您选择参与期权交易。为确保您充分了解期权交易的法律法规、风险及责任，请仔细阅读以下内容。
        </div>

        <div class="section">
          <div class="section_title">一、期权基本定义</div>
          <div class="section_content">
            <p>
              <span class="highlight">期权（Option）</span
              >：是一种金融衍生品合约，赋予买方在约定时间内以约定价格买入或卖出标的资产的权利（非义务），卖方则有义务履行合约。
            </p>
            <p>
              <span class="highlight">分类</span
              >：包括认购期权（看涨）、认沽期权（看跌），按行权时间可分为欧式期权（到期日行权）和美式期权（期限内随时行权）。
            </p>
          </div>
        </div>

        <div class="section">
          <div class="section_title">二、风险提示</div>
          <div class="section_content">
            <p><span class="highlight">市场风险</span></p>
            <p>
              期权价格受标的资产价格波动、时间价值衰减、波动率变化等因素影响，可能导致本金损失。
            </p>

            <p><span class="highlight">杠杆风险</span></p>
            <p>
              期权交易具有杠杆效应，小额资金可能放大收益或亏损，极端情况下可能损失全部权利金。
            </p>

            <p><span class="highlight">流动性风险</span></p>
            <p>
              部分期权合约交易量较低，可能面临无法及时平仓或成交价格偏离预期的风险。
            </p>

            <p><span class="highlight">行权与交割风险</span></p>
            <p>
              买方需关注行权规则（如欧式/美式），卖方可能被强制履约，需确保有足够资金或标的资产。
            </p>

            <p><span class="highlight">操作风险</span></p>
            <p>
              因操作失误（如未及时行权、错误下单等）导致的损失由投资者自行承担。
            </p>
          </div>
        </div>
      </div>
    </article>
  </section>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    back() {
      this.$router.back();
    }
  }
};
</script>

<style scoped>
.content {
  background-color: #fff;
  padding: 0.15rem;
  overflow-y: auto;
}

.notice_container {
  font-size: 0.16rem;
  line-height: 1.6;
  color: #333;
}

.notice_title {
  font-size: 0.18rem;
  font-weight: 500;
  margin-bottom: 0.1rem;
}

.notice_intro {
  margin-bottom: 0.2rem;
}

.section {
  margin-bottom: 0.2rem;
}

.section_title {
  font-size: 0.17rem;
  font-weight: 500;
  margin-bottom: 0.1rem;
  color: #333;
}

.section_content {
  padding-left: 0.05rem;
}

.highlight {
  font-weight: 500;
  color: #333;
}

p {
  margin-bottom: 0.1rem;
}
</style>
