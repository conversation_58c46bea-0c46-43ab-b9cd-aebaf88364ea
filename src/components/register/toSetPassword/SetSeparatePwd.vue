<template>
  <article class="content">
    <div style="margin-top: 0.08rem">
      <div class="pro_sl_item">
        <div class="tit">
          <h5>
            {{ fundAccountInfo.label }}: {{ fundAccountInfo.fundAccount }}
          </h5>
        </div>
        <div class="cont" v-if="enable">
          <div class="input_text pword">
            <h-keypanel
              :key="viewShowCount"
              v-model="trade_password"
              type="tel2"
              :mask="!show_trade_password"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="设置6位交易数字密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
            <a
              class="icon_eye"
              :class="show_trade_password ? 'show' : ''"
              @click="show_trade_password = !show_trade_password"
            ></a>
          </div>
          <div class="input_text pword">
            <h-keypanel
              :key="viewShowCount"
              v-model="confirm_trade_password"
              :length="6"
              type="tel2"
              :mask="!show_trade_password"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="重复6位交易数字密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
          </div>
        </div>
        <div class="cont" v-else>
          <div class="input_text pword">
            <h-keypanel
              :key="viewShowCount"
              v-model="trade_password"
              type="tel2"
              :mask="!show_trade_password"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="请输入原交易密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
            <a
              class="icon_eye"
              :class="show_trade_password ? 'show' : ''"
              @click="show_trade_password = !show_trade_password"
            ></a>
          </div>
        </div>
      </div>
      <div class="form_tips" v-if="enable">
        <p>温馨提示:</p>
        <p>为了您的资金安全，以下密码不能设置使用。</p>
        <p>1、三位数重复2次，如-123123</p>
        <p>2、后三位末前三位倒序数，如-123321</p>
        <p>3、两位数重复3次，如-121212</p>
        <p>4、一和二，三和四，五和六位相同，如-112233</p>
        <p>5、一二三位，四五六位相同，如-111222</p>
        <p>6、六位连续升序或降序的数字，如-123456，654321</p>
      </div>
      <div class="form_tips" v-else>
         <p>温馨提示:</p>
        <p>若您已启用独立密码，如需修改请前往修改交易密码。</p>
      </div>
    </div>
  </article>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { creditPwdCheck } from '@/service/service.js';
import { WEAKPWD } from '@/plugins/weak-pwd/WEAKPWD-min';
import { getPwdEncryption } from '@/common/util.js';
import { nativeFunc60094 } from '@/nativeShell/h5CallNative';

export default {
  name: 'SetSeparatePwd',
  inject: ['eventMessage', 'tkFlowInfo'],
  props: {
    enable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fundAccountInfo: {},
      trade_password: '',
      show_trade_password: false,
      confirm_trade_password: '',
      viewShowCount: 0
    };
  },
  computed: {
    disabled() {
      if (this.enable) {
        return this.trade_password === '' || this.confirm_trade_password === '';
      } else {
        return this.trade_password === '';
      }
    }
  },
  watch: {
    disabled: {
      handler(f) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          btnStatus: f ? 0 : 2,
          data: () => {
            this.verifyPwd();
          }
        });
      },
      immediate: true
    }
  },
  created() {
    this.init();
  },
  mounted() {},
  destroyed() {
    nativeFunc60094({
      isInterceptScreenshot: '0'
    });
    document.removeEventListener('visibilitychange', this.viewHideActivate);
  },
  methods: {
    viewHideActivate() {
      if (document.visibilityState === 'hidden') {
        // 页面进入后台时执行的代码
        this.trade_password = '';
        this.confirm_trade_password = '';
        this.viewShowCount++;
        console.log('页面进入后台');
      }
    },
    init() {
      nativeFunc60094({
        isInterceptScreenshot: '1',
        screenCaptureTip: '请妥善保存您的账号及密码，您保存的图片内容可能涉及到敏感信息，请请勿发送给他人'
      });
      document.addEventListener('visibilitychange', this.viewHideActivate);

      const { inProperty } = this.tkFlowInfo();
      this.fundAccountInfo = JSON.parse(
        inProperty.extInitParams
      ).fundAccountInfo;
    },
    verifyPwd() {
      if (this.enable) {
        if (
          !this.verifyYes(this.trade_password) ||
          !this.verifyYes(this.confirm_trade_password, '确认') ||
          !this.verifySame('trade')
        )
          return;
      } else {
        if (!this.verifyYes(this.trade_password)) return;
      }

      this.submit();
    },
    async submit() {
      try {
        const { specialFlag, fundAccount, assetProp, mainFlag } =
          this.fundAccountInfo;
        if (!this.enable) {
          const { code } = await creditPwdCheck({
            flowToken: sessionStorage.getItem('TKFlowToken'),
            account: fundAccount,
            assetProp,
            password: 'encrypt:' + getPwdEncryption(this.trade_password),
            clientPwdType: '2' //密码类型 1.资金密码  2交易密码
          });
          if (code !== 0) return;
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            selected_accounts_data: JSON.stringify([
              {
                specialFlag,
                fundAccount,
                assetProp,
                mainFlag
              }
            ])
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            selected_accounts_data: JSON.stringify([
              {
                specialFlag,
                fundAccount,
                assetProp,
                mainFlag,
                tradePassword: getPwdEncryption(this.trade_password)
              }
            ])
          });
        }
      } catch (err) {
        this.$TAlert({
          title: '温馨提示',
          tips: err
        });
      }
    },
    verifyYes(pwd, type = '') {
      if (pwd.length < 6) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请输入6位数字密码'
        });
        return false;
      }
      if (WEAKPWD(pwd).status) {
        this.errTxt = `您输入的${type}新密码格式有误`;
        this.$TAlert({
          title: '温馨提示',
          tips: this.errTxt
        });
        return false;
      }
      return true;
    },
    //判断两次密码设置是否一致
    verifySame() {
      let pwd1 = this.trade_password,
        pwd2 = this.confirm_trade_password;
      if (pwd1 !== pwd2) {
        this.$TAlert({
          title: '温馨提示',
          tips: '您输入的密码不一致'
        });
        return false;
      }
      return true;
    },
    getAssetPropMap(a) {
      let getMap = new Map();
      getMap.set(ASSET_PROP.DERIVATIVES_ACCOUNT, '衍生品资金账户');
      getMap.set(ASSET_PROP.ORDINARY_ACCOUNT, '普通资金账户');
      getMap.set(ASSET_PROP.CREDIT_ACCOUNT, '信用资金账户');
      getMap.set(ASSET_PROP.OPTIONS_ACCOUNT, '期权资金账户');
      getMap.set(ASSET_PROP.FUND_ACCOUNT, '基金资金账户');
      return getMap.get(a) || '';
    }
  }
};
</script>

<style scoped>
div.input_text > .hui-keypanel {
  padding: 0rem;
}
</style>
