<template>
  <section class="main fixed" style="position: fixed">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" href="javascript:void(0);"></a>
        <h1 class="title">重置密码</h1>
      </div>
    </header>
    <article class="content">
      <div class="set_pword_item">
        <div class="com_title">
          <h5>
            请设置新交易密码<em class="small">交易密码用于证券交易等环节</em>
          </h5>
        </div>
        <div class="input_form">
          <div class="input_text pword">
            <h-keypanel
              :key="viewShowCount"
              v-model="trade_password"
              type="tel2"
              :length="6"
              :mask="!show_trade_confirm"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="设置6位交易数字密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
            <a
              class="icon_eye"
              :class="show_trade_confirm ? 'show' : ''"
              @click="show_trade_confirm = !show_trade_confirm"
            ></a>
          </div>
          <div class="input_text">
            <h-keypanel
              :key="viewShowCount"
              v-model="confirm_trade_password"
              type="tel2"
              :length="6"
              :mask="!show_trade_confirm"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="重复6位交易数字密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
          </div>
        </div>
      </div>
      <div class="pwors_select">
        <p>资金密码与交易密码一致</p>
        <div class="switch">
          <input v-model="is_only_tradepwd" type="checkbox" />
          <div class="switch-inner">
            <div class="switch-arrow"></div>
          </div>
        </div>
      </div>
      <div
        v-show="!is_only_tradepwd"
        class="set_pword_item"
        style="display: block"
      >
        <div class="com_title">
          <h5>请设置新资金密码<em class="small">资金密码用于资金划转</em></h5>
        </div>
        <div class="input_form">
          <div class="input_text pword">
            <h-keypanel
              :key="viewShowCount"
              v-model="fund_password"
              type="tel2"
              :length="6"
              :mask="!show_fund_password"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="设置6位资金数字密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
            <a
              class="icon_eye"
              :class="show_fund_password ? 'show' : ''"
              @click="show_fund_password = !show_fund_password"
            ></a>
          </div>
          <div class="input_text">
            <h-keypanel
              :key="viewShowCount"
              v-model="confirm_fund_password"
              type="tel2"
              :length="6"
              :mask="!show_fund_password"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="重复6位资金数字密码"
            >
              <div slot="head" class="safe-head">
                <img src="../../../assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
          </div>
        </div>
      </div>
      <div class="pword_tips">
        <p>温馨提示：</p>
        <p>为了您的资金安全，以下密码不能设置使用。</p>
        <p>1、三位数重复2次，如-123123</p>
        <p>2、后三位末前三位倒序数，如-123321</p>
        <p>3、两位数重复3次，如-121212</p>
        <p>4、一和二，三和四，五和六位相同，如-112233</p>
        <p>5、一二三位，四五六位相同，如-111222</p>
        <p>6、六位连续升序或降序的数字，如-123456， 654321</p>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" :class="{ disabled: disabled }" @click="toNext"
          >确认重置</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { getPwdEncryption } from '@/common/util.js';
import { pwdSyncTypeQry } from '@/service/service.js';
import { EVENT_NAME } from '@/common/formEnum';
import { WEAKPWD } from '@/plugins/weak-pwd/WEAKPWD-min';
import '@/nativeShell/nativeCallH5';
import { nativeFunc60094 } from '@/nativeShell/h5CallNative';

export default {
  name: 'ToSetPassword',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      trade_password: '',
      confirm_trade_password: '',
      show_trade_confirm: false,
      fund_password: '',
      show_fund_password: false,
      confirm_fund_password: '',
      is_only_tradepwd: true,
      canChange: false,
      viewShowCount: 0
    };
  },
  computed: {
    disabled() {
      let filterList = [this.trade_password, this.confirm_trade_password];
      if (!this.is_only_tradepwd) {
        filterList.push(this.fund_password, this.confirm_fund_password);
      }
      return !filterList.every((v) => v !== '');
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    nativeFunc60094({
      isInterceptScreenshot: '1',
      screenCaptureTip: '请妥善保存您的账号及密码，您保存的图片内容可能涉及到敏感信息，请请勿发送给他人'
    });
    document.addEventListener('visibilitychange', this.viewHideActivate);
  },
  mounted() {
    pwdSyncTypeQry().then((res) => {
      this.is_only_tradepwd = res.data.pwdSynchronizationType;
      if (this.is_only_tradepwd === '1') {
        this.is_only_tradepwd = true;
        this.canChange = false;
      } else {
        this.is_only_tradepwd = false;
        this.canChange = true;
      }
    });
  },
  destroyed() {
    nativeFunc60094({
      isInterceptScreenshot: '0'
    });
    document.removeEventListener('visibilitychange', this.viewHideActivate);
  },
  methods: {
    viewHideActivate() {
      if (document.visibilityState === 'hidden') {
        // 页面进入后台时执行的代码
        this.trade_password = '';
        this.confirm_trade_password = '';
        this.fund_password = '';
        this.confirm_fund_password = '';
        this.viewShowCount++;
        console.log('页面进入后台');
      }
    },
    sameClick(e) {
      if (!this.canChange) {
        e.preventDefault();
        return;
      }
      if (this.is_only_tradepwd) {
        this.fund_password = this.trade_password;
        this.confirm_fund_password = this.fund_password;
      } else {
        this.fund_password = '';
        this.confirm_fund_password = this.fund_password;
      }
    },

    sameCheck() {
      if (this.is_only_tradepwd) {
        this.fund_password = this.trade_password;
        this.confirm_fund_password = this.fund_password;
      }
    },

    verifyYes(pwd, type = '') {
      if (pwd.length < 6) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请输入6位数字密码'
        });
        return false;
      }
      if (WEAKPWD(pwd).status) {
        this.errTxt = `您输入的${type}新密码格式有误`;
        this.$TAlert({
          title: '温馨提示',
          tips: this.errTxt
        });
        return false;
      }
      return true;
      /*  if (!/^\d{6}$/.test(pwd)) {
        this.errTxt = `您输入的${type}新密码格式有误`;
      } else if (/(\d)\d*\1\d*\1/.test(pwd)) {
        this.errTxt = `您输入的${type}新密码格式有误`;
      } else if (/(\d{2,})\1|(\d)\2{2,}/.test(pwd)) {
        this.errTxt = `您输入的${type}新密码格式有误`;
      } else if (
        /(?:1234|2345|3456|4567|5678|6789|7890|9876|8765|7654|6543|5432|4321)/.test(
          pwd
        )
      ) {
        this.errTxt = `您输入的${type}新密码格式有误`;
      } else {
        this.errTxt = 'pass';
      }
      if (this.errTxt !== 'pass') {
        this.$TAlert({
          tips: this.errTxt
        });
        return false;
      }
      return true; */
    },
    //判断两次密码设置是否一致
    verifySame(type) {
      let pwd1;
      let pwd2;
      if (type === 'trade') {
        pwd1 = this.trade_password;
        pwd2 = this.confirm_trade_password;
      } else if (type === 'fund') {
        pwd1 = this.fund_password;
        pwd2 = this.confirm_fund_password;
      }
      if (pwd1 !== pwd2) {
        this.$TAlert({
          tips: '您输入的密码不一致'
        });
        return false;
      }
      return true;
    },

    toNext() {
      if (this.disabled) return;
      this.sameCheck();
      if (
        !this.verifyYes(this.trade_password) ||
        !this.verifyYes(this.confirm_trade_password, '确认') ||
        !this.verifyYes(this.fund_password) ||
        !this.verifyYes(this.confirm_fund_password, '确认') ||
        !this.verifySame('trade') ||
        !this.verifySame('fund')
      ) {
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        tradePassword: getPwdEncryption(this.trade_password),
        fundPassword: getPwdEncryption(this.fund_password),
        isOnlyTradepwd: this.is_only_tradepwd === true ? '1' : '0'
      });
    }
  }
};
</script>
<style scoped>
div.input_text > .hui-keypanel {
  padding: 0;
}
div.input_text >>> .hui-keypanel-input-placeholder {
  color: #c8c9cc;
}
</style>
