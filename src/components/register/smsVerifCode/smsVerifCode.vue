<template>
  <article class="content">
    <div class="info_compage">
      <div class="com_title">
        <h5>{{ comTitle }}</h5>
      </div>
      <div class="input_form">
        <div class="input_text text">
          <span class="tit active">预留手机号</span>
          <input
            v-model="phoneNumInput"
            ref="phoneNumInput"
            class="t1"
            type="tel"
            maxlength="11"
            :disabled="desensitize"
          />
          <a
            v-if="$attrs.modify_mobile_num"
            class="code_btn2"
            @click="modifyMobile"
            >修改
          </a>
          <a
            v-else-if="$attrs.en_change_mobile_tel"
            class="code_btn2"
            @click="changeMobile"
            >变更手机号
          </a>
        </div>
        <div class="input_text text code">
          <span class="tit active">验证码</span>
          <input
            id="smsCode"
            v-model="smsCode"
            @input="smsCode = smsCode.replace(/[^\d]/g, '')"
            class="t1"
            type="tel"
            maxlength="6"
            placeholder="请输入短信验证码"
            autocomplete="off"
          />
          <sms-code-btn
            v-model="uuid"
            :need-img-code="false"
            :mobile-no="phone"
            biz-type="010200"
            @send-result="SMSCodeCallback"
          />
        </div>
      </div>
      <div v-if="$attrs.suport_voice" class="cond_tips">
        短信验证码收不到？试试<a @click="smsVoice" class="com_link"
          >语音验证码</a
        >吧！
      </div>
    </div>
  </article>
</template>

<script>
import SmsCodeBtn from '@/components/SmsCodeBtn.vue';
import { smsCodeVerification, clientInfoQryV2 } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { formatMobileNo } from '@/common/filter';
import HmosUtil from '@/common/HmosUtil';
const hmosUtil = new HmosUtil({});
import pageIdentifierBus from '@/common/tracking/pageIdentifierBus';
import { trackBusinessPageClick } from '@/common/tracking/businessTracking';

export default {
  name: 'smsVerifCode',
  inject: ['eventMessage'],
  components: {
    SmsCodeBtn
  },
  data() {
    return {
      phone: '',
      smsCode: '',
      imgSrc: '',
      uuid: '',
      desensitize: true //是否手机号脱敏
    };
  },
  props: {
    com_title: {
      type: String,
      default: ''
    }
  },
  computed: {
    phoneNumInput: {
      get() {
        if (this.desensitize) {
          return formatMobileNo(this.phone);
        } else {
          return this.phone;
        }
      },
      set(val) {
        this.phone = val;
      }
    },
    comTitle() {
      return this.com_title === ''
        ? '请获取并输入短信验证码。如您的预留手机号已发生变更，请前往“个人资料”进行更新。'
        : this.com_title;
    }
  },
  watch: {
    smsCode(v) {
      if (v.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          text: '下一步',
          display: true,
          btnStatus: 2,
          data: () => {
            this.toNext();
          }
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
    }
  },
  created() {
    // 设置页面标识供容器埋点使用
    pageIdentifierBus.setPageIdentifier('sjhdxyz');
    this.$store.commit('flow/setWhiteBg', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  mounted() {
    this.renderingView();
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
  },
  methods: {
    viewShowCallBack() {
      if (this.$attrs.en_change_mobile_tel) {
        this.renderingView();
      }
    },
    renderingView() {
      clientInfoQryV2()
        .then((res) => {
          if (res.data.organFlag === '0') {
            //只有个人户获取手机号
            this.phone = res.data.mobileTel;
          }
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    back() {
      this.$router.back();
    },

    SMSCodeCallback(flag) {
      trackBusinessPageClick({
        bizType: $h.getSession('bizType'),
        pageIdentifier: 'sjhdxyz',
        elementName: 'hqyzm'
      });
      console.log(flag);
      if (!flag) {
        this.uuid = '';
      }
    },

    changeMobile() {
      trackBusinessPageClick({
        bizType: $h.getSession('bizType'),
        pageIdentifier: 'sjhdxyz',
        elementName: 'bgsjh'
      });

      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
      return;
      if ($hvue.platform === '0') {
        window.location.href = $hvue.customConfig.thirdPartyUrl.basicInfo;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: 'open',
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.basicInfo,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    modifyMobile() {
      this.desensitize = false;
      this.$nextTick(() => {
        this.$refs.phoneNumInput.focus();
      });
    },

    smsVoice() {
      trackBusinessPageClick({
        bizType: $h.getSession('bizType'),
        pageIdentifier: 'sjhdxyz',
        elementName: 'yyyyzm'
      });
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先获取短信验证码'
        });
        return false;
      }
      _hvueConfirm({
        title: '温馨提示',
        mes: '亲，您可以使用输入的手机号，拨打95310-按3-按1，收听语音验证码。',
        opts: [
          {
            txt: '取消',
            color: '#333333'
          },
          {
            txt: '立即拨打',
            callback: () => {
              if (hmosUtil.checkHM) {
                hmosUtil.callPhone('95310');
              } else if ($hvue.platform === '0') {
                window.location.href = 'tel:95310';
              } else {
                let reqParams = {
                  funcNo: '50220',
                  telNo: '95310',
                  callType: '0'
                };
                console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
                const res = $h.callMessageNative(reqParams);
                console.log(`请求结果为: ~~${JSON.stringify(res)}`);
              }
            }
          }
        ]
      });
    },

    toNext() {
      if (!this.uuid) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请先获取短信验证码'
        });
        return false;
      }
      if (this.smsCode.length !== 6) {
        _hvueToast({
          mes: '短信验证码不匹配'
        });
        return false;
      }
      smsCodeVerification({
        mobile: this.phone,
        captchaCode: this.smsCode,
        serialNumber: this.uuid
      })
        .then((res) => {
          if (res.data.verificationvFlag !== '1') {
            _hvueToast({ mes: '输入的验证码有误，请重新输入' });
            this.smsCode = '';
            return;
          }
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            verif_mobile_tel: this.phone
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    }
  }
};
</script>
