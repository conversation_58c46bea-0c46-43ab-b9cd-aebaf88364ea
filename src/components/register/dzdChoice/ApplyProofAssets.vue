<template>
  <Fragment>
    <div class="input_form">
      <div class="input_text text">
        <span class="tit active">资产类型</span>
        <input class="t1" type="text" value="实时资产" readonly />
      </div>
      <div class="input_text text">
        <div class="tit">资金账号</div>
        <input class="t1 gray" type="text" v-model="fundAccount" readonly />
      </div>
      <div class="input_text text">
        <div class="tit">手机号码</div>
        <input class="t1 gray" type="text" v-model="mobileTelFormat" readonly />
        <a class="code_btn2" @click="modifyMobile">{{ modifyMobileBtn }}</a>
      </div>
      <div class="s_form_tips">{{ tips }}</div>
    </div>
  </Fragment>
</template>

<script>
import { querySysConfig, businessAcceptInfoQry } from '@/service/service';
import { DICT_TYPE } from '@/common/enumeration';
import { formatMobileNo } from '@/common/filter';
import { rules } from '@/common/rule';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'ApplyProofAssets',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      assetCertApplyMaxCount: 0
    };
  },
  props: {
    fundAccount: {
      type: String,
      default: ''
    },
    mobileTel: {
      type: String,
      default: ''
    }
  },
  watch: {
    mobileTelFormat: {
      handler(nv) {
        if (nv === '') {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '提交申请',
            btnStatus: 0
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '提交申请',
            btnStatus: 2,
            data: () => {
              this.submit();
            }
          });
        }
      },
      immediate: true
    }
  },
  computed: {
    tips() {
      if (this.modifyMobileBtn === '去完善') {
        return '未查询到您的手机号，请点击去完善进行补充后，在提交申请。';
      } else if (this.modifyMobileBtn === '去修改') {
        return '查询到您的手机号格式不正确，请点击去修改后再提交申请。';
      } else {
        return `您当天最多可提交${this.assetCertApplyMaxCount}次申请！`;
      }
    },
    modifyMobileBtn() {
      if (this.mobileTel === '') {
        return '去完善';
      } else if (!rules.mobileTel.validate(this.mobileTel)) {
        return '去修改';
      } else {
        return '修改';
      }
    },
    mobileTelFormat() {
      if (['去完善', '去修改'].includes(this.modifyMobileBtn)) {
        return '';
      } else {
        return formatMobileNo(this.mobileTel);
      }
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
    this.getSysConfig();
  },
  methods: {
    async submit() {
      const { bizType } = this.tkFlowInfo();
      // 查询受理单列表，获取当天更新的受理单数量
      const { code, data } = await businessAcceptInfoQry({
          bizType,
          queryStatus: '1,3' //查询状态：0：受理中，1：受理完成，2：受理作废，3：办理完成
        });
      if (code !== 0) {
        this.$TAlert({
          mes: data
        });
        return;
      }
      const flowInsList = data.flowInsList;
      // 筛选出更新时间为当天的记录
      const todayFlowList = flowInsList.filter(item => {
        const dateRegex = /^\d{4}-\d{2}-\d{2}/;
        // 获取当前日期的年月日字符串，格式为YYYY-MM-DD
        const today = (new Date().toISOString().match(dateRegex) || [''])[0];
        // 使用正则表达式提取updateTime中的日期部分（YYYY-MM-DD）
        const updateDate = (item.updateTime.match(dateRegex) || [''])[0];
        // 比较日期是否为当天
        return updateDate === today;
      });
      if(todayFlowList.length >= Number(this.assetCertApplyMaxCount)) {
        this.$TAlert({
          title: '温馨提示',
          tips: '对不起，您当日的资产证明查询次数已经用完，请下个交易日再来'
        });
        return;
      }
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    },
    getSysConfig() {
      const configKey = DICT_TYPE.ASSET_CERT_APPLY_MAX_COUNT;
      querySysConfig({ configKey })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            try {
              this.assetCertApplyMaxCount = data[configKey].configValue;
            } catch (error) {
              console.error(error);
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            mes: err
          });
        });
    },
    modifyMobile() {
      // 跳转至个人资料
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010004', initJumpMode: '0' });
      });
    }
  }
};
</script>

<style scoped>
.s_form_tips {
  line-height: 0.2rem;
  font-size: 0.14rem;
  color: #55555e;
}
.t1.gray {
  color: #87878d;
}
</style>
