<template>
  <fragment>
    <div v-if="!onlyShowOne && loadingEd" class="com_box">
      <ul class="protocol_list">
        <li
          v-for="({ agreementName, agreementNo }, index) in agreeList"
          :key="index"
          :class="{
            readed: readList.includes(agreementNo),
            unread: !readList.includes(agreementNo)
          }"
          @click="openAgree(index)"
        >
          <a>《{{ agreementName }}》</a>
          <span class="state">{{
            readList.includes(agreementNo) ? '已读' : '未读'
          }}</span>
        </li>
      </ul>
    </div>
    <!-- <div v-if="!onlyShowOne && loadingEd" class="rule_check">
      <span
        class="icon_check"
        :class="{ checked: isChecked }"
        @click="selectAgree"
      ></span
      >本人已详细阅读并完全理解以上合同及协议，同意签署。
    </div> -->
    <onlyProtocolDetail
      v-if="onlyShowOne"
      v-model="agreeIndex"
      v-bind="$attrs"
      :show="onlyShowOne"
      :agree-list="agreeList"
      :is-count="!isCount"
      @callback="agreeCallBack"
    />
    <protocol-detail
      v-if="showAgreeDetail"
      v-model="agreeIndex"
      :agree-list="agreeList"
      :read-list="readList"
      :is-count="!isCount"
      @callback="agreeCallBack"
      @signAgree="detailSignAgree"
    />
  </fragment>
</template>

<script>
import { queryAgreementExtV3 } from '@/service/service';
import ProtocolDetail from './components/ProtocolDetail';
import onlyProtocolDetail from './components/onlyProtocolDetail.vue';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgreeV2 } from '@/common/util';

export default {
  name: 'ProtocolListForInExchangeV4',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  components: {
    onlyProtocolDetail,
    ProtocolDetail
  },
  data() {
    return {
      onlyShowOne: false,
      loadingEd: false,
      showAgreeDetail: false,
      isChecked: false,
      agreeIndex: 0,
      readList: [],
      agreeList: [],
      epaperESignedFlag: ''
    };
  },
  computed: {
    isCount() {
      return this.readList.includes(this.agreeId);
    },
    agreeId() {
      return this.agreeList[this.agreeIndex]?.agreementNo;
    },
    allReadFlag() {
      console.log(this.agreeList);
      return this.agreeList.every((a) => {
        return this.readList.includes(a.agreementNo);
      });
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this._queryAgreement();
    this.clearKeepAlive();
  },
  methods: {
    getProtocolParam() {
      let param = {
        agreementBizType: this.$attrs.agreementNodeNo.split(':')[0],
        agreementNodeNo: this.$attrs.agreementNodeNo.split(':')[1],
        agreementExt: this.$attrs.agreementExt
      };
      try {
        // let protocolParam = JSON.parse(this.$attrs.protocolParam);
        // protocolParam.forEach(({ groupId, contractType }) => {
        //   param.groupId.push(groupId);
        //   param.contractType.push(contractType);
        // });
        return param;
      } catch (e) {
        console.log(e);
        return param;
      }
    },

    async _queryAgreement() {
      const { inProperty } = this.tkFlowInfo();
      let param = this.getProtocolParam();
      let reqParams = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        ...param
        // bizType: '013455',
        // contractType: param.contractType.join(','),
        // groupId: param.groupId.join(',')
        // agreementNo: agreementNos
      };
      queryAgreementExtV3(reqParams)
        .then((res) => {
          const agreementList = res.data.agreementList;
          this.epaperESignedFlag = res.data.epaperESignedFlag;
          this.agreeList = agreementList.map((it, i) => {
            it.id = i;
            it.agreementBizType = param.agreementBizType;
            it.epaperESignedFlag = this.epaperESignedFlag;
            return it;
          });
          if (this.agreeList.length === 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            return;
          }
          if (this.agreeList.length === 1) {
            this.onlyShowOne = true;
          } else {
            this.onlyShowOne = false;
          }
          this.loadingEd = true;
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submitForm
          });
        })
        .catch((err) => {
          _hvueToast({
            mes: err
          });
        });
    },
    openAgree(i) {
      this.showAgreeDetail = true;
      this.agreeIndex = i;
    },
    agreeCallBack(flag, notClose) {
      console.log(flag);
      if (flag) {
        if (!this.isCount && !this.readList.includes(this.agreeId)) {
          this.readList.push(this.agreeId);
        }
        this.isChecked = this.allReadFlag;
        if (
          (this.allReadFlag || this.agreeIndex + 1 === this.agreeList.length) &&
          notClose
        ) {
          this.showAgreeDetail = false;
        }
      } else {
        this.showAgreeDetail = false;
      }
    },
    selectAgree() {
      let firstKey = this.agreeList.filter((item) => {
        return !this.readList.includes(item.agreementNo);
      })[0];
      let firstIndex = 0;
      if (firstKey) {
        firstIndex = this.agreeList.findIndex((item) => {
          return item.agreementNo === firstKey.agreementNo;
        });
      }
      if (!this.allReadFlag) {
        this.showAgreeDetail = true;
        this.agreeIndex = firstIndex;
        return;
      }
      this.isChecked = !this.isChecked;
    },
    detailSignAgree() {
      this.isChecked = true;
      this.submitForm();
    },
    submitForm() {
      if (!this.isChecked) {
        _hvueToast({
          mes: '请阅读并勾选协议'
        });
        return;
      }

      const tkFlowInfo = this.tkFlowInfo();
      signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            epaperSignJson,
            epaperESignedFlag: this.epaperESignedFlag
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>

<style scoped></style>
