<template>
  <fragment>
    <div class="com_title">
      <h5>请根据您的实际情况申请开通创业板权限</h5>
    </div>
    <ul class="type_navlist">
      <li
        v-for="({ subBizType, desc, title }, k) in viewList"
        :key="k"
        @click="select(subBizType)"
      >
        <h5>{{ title }}</h5>
        <p>{{ desc }}</p>
      </li>
    </ul>
  </fragment>
</template>

<script>
import { querySysConfig } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { DICT_TYPE } from '@/common/enumeration';

export default {
  name: 'CybSubBizTypeChoice',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      viewList: []
    };
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const configKey = DICT_TYPE.CYB_SUB_BIZ_TYPE_CONFIG;
      querySysConfig({ configKey })
        .then(({ data, code, msg }) => {
          if (code === 0) {
            try {
              this.viewList = JSON.parse(data[configKey].configValue);
            } catch (error) {
              console.error(error);
              this.viewList = [];
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            mes: err
          });
        });
    },
    async select(subBizType) {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        subBizType
      });
    }
  }
};
</script>

<style></style>
