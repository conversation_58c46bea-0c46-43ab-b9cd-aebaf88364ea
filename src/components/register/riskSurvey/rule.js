export const ruleList = [
  // 第2题选择A或B，第3题选择C或D,非强制互斥
  {
    questionId: '889',
    answerIdList: ['1', '2'],
    ruleType: '00',
    ruleNo: '01',
    questionList: [
      {
        questionId: '890',
        answerIds: ['5', '6']
      }
    ]
  },
  {
    questionId: '890',
    answerIdList: ['5', '6'],
    ruleType: '00',
    ruleNo: '01',
    questionList: [
      {
        questionId: '889',
        answerIds: ['1', '2']
      }
    ]
  },
  // 第12题选择包括BCD选项中的一项或两项，第13题选择A,非强制互斥
  {
    questionId: '1027',
    answerIdList: ['2', '3', '4'],
    ruleType: '00',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '1028',
    answerIdList: ['1'],
    ruleType: '00',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1027',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  // 第12题选择包括BCD选项中的一项或两项，第15题选择A,非强制互斥
  {
    questionId: '1027',
    answerIdList: ['2', '3', '4'],
    ruleType: '00',
    ruleNo: '01',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['1'],
    ruleType: '00',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1027',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  // 第13题CD任意一项，第14题选择A（非强制互斥）
  {
    questionId: '1028',
    answerIdList: ['3', '4'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '897',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '897',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['3', '4']
      }
    ]
  },
  // 第13题选择A，第15题选择C（非强制互斥）
  {
    questionId: '1028',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['3']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['3'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['1']
      }
    ]
  },
  // 第14题A，第15题CD任意一项（非强制互斥）
  {
    questionId: '897',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['3', '4']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['3', '4'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '897',
        answerIds: ['1']
      }
    ]
  },
  // 第14题BCDE任意一项，第15题选择A（非强制互斥）
  {
    questionId: '897',
    answerIdList: ['2', '3', '4', '5'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '897',
        answerIds: ['2', '3', '4', '5']
      }
    ]
  },
  // 第13题选择为A，第14题CDE任意一项（非强制互斥）
  {
    questionId: '1028',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '897',
        answerIds: ['3', '4', '5']
      }
    ]
  },
  {
    questionId: '897',
    answerIdList: ['3', '4', '5'],
    ruleType: '01',
    ruleNo: '01',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['1']
      }
    ]
  },
  // 以下为强制互斥
  // 第15题选择A，第13题选择为BCD选项中的一项（强制互斥）
  {
    questionId: '8020',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  // 第7题A选项，与BCD任意一项不可以同时选择
  {
    questionId: '899',
    answerIdList: ['1'],
    ruleType: '02',
    ruleNo: '02',
    questionList: [
      {
        questionId: '899',
        answerIds: ['2', '3', '4']
      }
    ]
  },
  {
    questionId: '899',
    answerIdList: ['2', '3', '4'],
    ruleType: '02',
    ruleNo: '02',
    questionList: [
      {
        questionId: '899',
        answerIds: ['1']
      }
    ]
  },
  {
    questionId: '1028',
    answerIdList: ['2', '3', '4'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['1']
      }
    ]
  },
  // 第5题选择A，第17题选择E
  {
    questionId: '892',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1029',
        answerIds: ['5']
      }
    ]
  },
  {
    questionId: '1029',
    answerIdList: ['5'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '892',
        answerIds: ['1']
      }
    ]
  },
  // 第6题选择C，第18题选择DEFG任意一项（强制互斥）
  {
    questionId: '1025',
    answerIdList: ['3'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1030',
        answerIds: ['4', '5', '6', '7']
      }
    ]
  },
  {
    questionId: '1030',
    answerIdList: ['4', '5', '6', '7'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1025',
        answerIds: ['3']
      }
    ]
  },
  // 第13题选择为A，第15题选择D（强制互斥）
  {
    questionId: '1028',
    answerIdList: ['1'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '8020',
        answerIds: ['4']
      }
    ]
  },
  {
    questionId: '8020',
    answerIdList: ['4'],
    ruleType: '01',
    ruleNo: '02',
    questionList: [
      {
        questionId: '1028',
        answerIds: ['1']
      }
    ]
  }
];

/**
 * 互斥逻辑处理类
 */
export class MutualExclusionHandler {
  constructor(questionList, answerList, $set, tkFlowInfo) {
    this.questionList = questionList;
    this.answerList = answerList;
    this.$set = $set;
    this.tkFlowInfo = tkFlowInfo;
  }

  /**
   * 检查互斥规则
   * @param {Object} item - 选择的选项
   * @param {number} quesIndex - 题目索引
   * @returns {Object} 互斥检查结果
   */
  checkMutualExclusion(item, quesIndex) {
    const result = {
      hasConflict: false,
      conflictType: '', // '01': 非强制互斥, '02': 强制互斥
      conflictList: [],
      tips: ''
    };

    // 检查规则配置的互斥
    this._checkRuleBasedExclusion(item, quesIndex, result);

    // 检查特殊互斥（年龄、职业等）
    this._checkSpecialExclusion(item, quesIndex, result);

    return result;
  }

  /**
   * 检查基于规则配置的互斥
   */
  _checkRuleBasedExclusion(item, quesIndex, result) {
    const processedQuestions = new Set(); // 用于记录已处理的题目，避免重复弹窗

    ruleList.forEach((ruleItem) => {
      if (
        item.questionNo === ruleItem.questionId &&
        ruleItem.answerIdList.includes(item.answerNo)
      ) {
        this.questionList.forEach((quesArr, qIndex) => {
          ruleItem.questionList.forEach((ruleQuesItem) => {
            // 检查是否已经处理过这个题目
            const questionKey = `${ruleQuesItem.questionId}_${ruleItem.ruleNo}`;
            if (processedQuestions.has(questionKey)) {
              return; // 跳过已处理的题目
            }

            // 检查这个题目是否有任何冲突的选中选项
            const conflictOptions = quesArr.filter(
              (quseItem) =>
                quseItem.questionNo === ruleQuesItem.questionId &&
                ruleQuesItem.answerIds.includes(quseItem.answerNo) &&
                quseItem.checked
            );

            if (conflictOptions.length > 0) {
              result.hasConflict = true;
              result.conflictType = ruleItem.ruleNo;
              processedQuestions.add(questionKey); // 标记为已处理

              // 合并所有冲突选项的内容
              const conflictContents = conflictOptions
                .map((option) => option.answerContent)
                .join('、');

              // 获取冲突题目的问题内容
              const conflictQuestionContent = quesArr[0].questionContent;

              // 生成冲突提示文案
              const tipsContext = {
                currentItem: item,
                currentQuestionIndex: quesIndex + 1,
                conflictQuestionIndex: qIndex + 1,
                conflictQuestionContent: conflictQuestionContent,
                ruleItem: ruleItem,
                targetQuestionId: ruleQuesItem.questionId
              };

              const conflictInfo = {
                questionIndex1: quesIndex + 1,
                questionIndex2: qIndex + 1,
                questionContent1: item.questionContent,
                questionContent2: conflictContents,
                conflictOptions: conflictOptions,
                tips: this._generateConflictTips(tipsContext)
              };

              result.conflictList.push(conflictInfo);
            }
          });
        });
      }
    });
  }

  /**
   * 检查特殊互斥（年龄、职业等）
   */
  _checkSpecialExclusion(item, quesIndex, result) {
    const questionIndex = quesIndex + 1;

    // 第1题年龄题互斥
    if (questionIndex === 1) {
      this._checkAgeConflict(item, result);
      this._checkProfessionConflict(item, result);
    }

    // 第8题投资经验与年龄互斥
    if (questionIndex === 8) {
      this._checkInvestmentExperienceConflict(item, result);
    }

    // 第7题历史答案对比互斥
    if (questionIndex === 7) {
      this._checkHistoricalAnswerConflict(item, result);
    }

    // 第18题历史学历对比互斥
    if (questionIndex === 18) {
      this._checkEducationDegreeConflict(item, result);
    }
  }

  /**
   * 检查年龄互斥
   */
  _checkAgeConflict(item, result) {
    const { inProperty } = this.tkFlowInfo();
    if (
      item.answerNo === '6' &&
      ((parseInt(inProperty.clientAge || 0) < 50 &&
        inProperty.clientGender === '0') ||
        (parseInt(inProperty.clientAge || 0) < 45 &&
          inProperty.clientGender === '1'))
    ) {
      result.hasConflict = true;
      result.conflictType = '01';
      result.conflictList.push({
        questionIndex1: 1,
        questionIndex2: '',
        questionContent1: item.questionContent,
        questionContent2: '',
        tips: '您的年龄与选项可能存在不相符，请再次确认信息真实准确或进行修改'
      });
    }
  }

  /**
   * 检查职业互斥
   */
  _checkProfessionConflict(item, result) {
    const { inProperty } = this.tkFlowInfo();
    if (
      ['7', '9'].includes(inProperty.professionCode) &&
      item.answerNo === '1'
    ) {
      result.hasConflict = true;
      result.conflictType = '01';
      result.conflictList.push({
        questionIndex1: 1,
        questionIndex2: '',
        questionContent1: item.questionContent,
        questionContent2: '',
        tips: '您的主要收入来源与您的职业可能不符，请确认勾选的信息准确无误'
      });
    }
  }

  /**
   * 检查投资经验与年龄互斥
   */
  _checkInvestmentExperienceConflict(item, result) {
    const { inProperty } = this.tkFlowInfo();
    if (item.answerNo === '4' && parseInt(inProperty.clientAge || 0) < 26) {
      result.hasConflict = true;
      result.conflictType = '01';
      result.conflictList.push({
        questionIndex1: 8,
        questionIndex2: '',
        questionContent1: item.questionContent,
        questionContent2: '',
        tips: '您的投资经验与您的年龄情况可能不符，请确认勾选的信息准确无误'
      });
    }
  }

  /**
   * 检查历史答案对比互斥（第7题）
   */
  _checkHistoricalAnswerConflict(item, result) {
    const { inProperty } = this.tkFlowInfo();
    const { oldPaperAnswer, answerDate } = inProperty;

    // 检查是否有历史答案和答题时间
    if (!oldPaperAnswer || !answerDate) {
      return;
    }

    // 检查是否在3个月内（90天）
    const currentDate = new Date();
    const historyDate = this._parseAnswerDate(answerDate);

    if (!historyDate) {
      return; // 日期格式无效，不检查
    }

    const daysDiff = Math.floor(
      (currentDate - historyDate) / (1000 * 60 * 60 * 24)
    );

    if (daysDiff < 0 || daysDiff > 90) {
      return; // 不在3个月内，不检查
    }

    // 解析历史答案
    const historyAnswers = this._parseHistoryAnswers(oldPaperAnswer);

    // 查找第7题的历史答案（题目编号可能是899或1078）
    const question7History = historyAnswers['899'] || historyAnswers['1078'];

    if (!question7History) {
      return; // 没有第7题的历史答案
    }

    // 检查当前选择的所有答案
    const currentAnswers = this._getCurrentAnswers(item.questionNo, item);

    // 检查是否从仅选A变为包含BCD任意一项
    if (this._isFromAOnlyToBCD(question7History, currentAnswers)) {
      result.hasConflict = true;
      result.conflictType = '01';
      result.conflictList.push({
        questionIndex1: 7,
        questionIndex2: '',
        questionContent1: item.questionContent,
        questionContent2: '',
        tips: '您的选项较上次测评结果变化较大，请再次确认信息真实准确或进行修改。'
      });
    }
  }

  /**
   * 解析answerDate日期格式
   * @param {string} answerDate - 格式如'20250718'
   * @returns {Date|null} 解析后的日期对象，失败返回null
   */
  _parseAnswerDate(answerDate) {
    if (!answerDate || typeof answerDate !== 'string') {
      return null;
    }

    // 处理'20250718'格式
    if (answerDate.length === 8 && /^\d{8}$/.test(answerDate)) {
      const year = answerDate.substring(0, 4);
      const month = answerDate.substring(4, 6);
      const day = answerDate.substring(6, 8);

      // 验证日期有效性
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

      // 检查日期是否有效（防止2月30日这种无效日期）
      if (
        date.getFullYear() === parseInt(year) &&
        date.getMonth() === parseInt(month) - 1 &&
        date.getDate() === parseInt(day)
      ) {
        return date;
      }
    }

    // 尝试其他常见格式
    try {
      // 尝试ISO格式或其他标准格式
      const date = new Date(answerDate);
      if (!isNaN(date.getTime())) {
        return date;
      }
    } catch (e) {
      // 忽略解析错误
    }

    return null; // 无法解析
  }

  /**
   * 解析历史答案字符串
   * 格式：题号_答案，如 "899_1|1027_1&2&3&4|1028_1"
   */
  _parseHistoryAnswers(oldPaperAnswer) {
    const answers = {};
    if (oldPaperAnswer) {
      // 按|分割每个题目的答案
      const answerPairs = oldPaperAnswer.split('|');
      answerPairs.forEach((pair) => {
        if (pair.includes('_')) {
          try {
            // 分割格式：题号_答案
            const [questionNo, answerPart] = pair.split('_');
            if (questionNo && answerPart) {
              // 处理答案部分，可能用&分割多个答案
              let answerList;
              if (answerPart.includes('&')) {
                answerList = answerPart.split('&');
              } else {
                answerList = [answerPart];
              }

              // 过滤空答案
              answerList = answerList.filter(
                (answer) => answer && answer.trim()
              );

              if (questionNo && answerList.length > 0) {
                answers[questionNo] = answerList;
              }
            }
          } catch (e) {
            // 忽略解析错误的项目
            console.warn('解析历史答案项目失败:', pair, e);
          }
        }
      });
    }
    return answers;
  }

  /**
   * 获取当前题目的所有选中答案
   * @param {string} questionNo - 题目编号
   * @param {Object} currentItem - 当前正在选择的选项（可选）
   */
  _getCurrentAnswers(questionNo, currentItem = null) {
    const currentQuestion = this.questionList.find(
      (q) => q[0].questionNo === questionNo
    );
    if (!currentQuestion) return [];

    // 获取已选中的答案
    const selectedAnswers = currentQuestion
      .filter((option) => option.checked)
      .map((option) => option.answerNo);

    // 如果提供了当前选择的选项，需要模拟选择状态
    if (currentItem && currentItem.questionNo === questionNo) {
      const currentAnswerNo = currentItem.answerNo;

      // 判断题目类型
      if (currentItem.questionKind === 'MULTI') {
        // 多选题：如果当前选项未选中，则添加到答案列表
        if (!selectedAnswers.includes(currentAnswerNo)) {
          selectedAnswers.push(currentAnswerNo);
        }
        // 如果已选中，则从答案列表中移除（取消选择）
        else {
          const index = selectedAnswers.indexOf(currentAnswerNo);
          if (index > -1) {
            selectedAnswers.splice(index, 1);
          }
        }
      } else {
        // 单选题：直接替换为当前选项
        return [currentAnswerNo];
      }
    }

    return selectedAnswers;
  }

  /**
   * 检查第7题的双向互斥逻辑
   * 1. 历史仅选A → 当前包含BCD → 互斥
   * 2. 历史包含BCD → 当前仅选A → 互斥
   */
  _isFromAOnlyToBCD(historyAnswers, currentAnswers) {
    // 历史答案是否仅选择A（answerNo为'1'）
    const historyOnlyA =
      historyAnswers.length === 1 && historyAnswers.includes('1');

    // 历史答案是否包含BCD任意一项
    const historyIncludesBCD = historyAnswers.some((answer) =>
      ['2', '3', '4'].includes(answer)
    );

    // 当前选择是否包含BCD任意一项（answerNo为'2','3','4'）
    const currentIncludesBCD = currentAnswers.some((answer) =>
      ['2', '3', '4'].includes(answer)
    );

    // 当前选择是否仅选择A
    const currentOnlyA =
      currentAnswers.length === 1 && currentAnswers.includes('1');

    // 双向互斥检查
    // 情况1: 历史仅选A → 当前包含BCD
    const case1 = historyOnlyA && currentIncludesBCD;

    // 情况2: 历史包含BCD → 当前仅选A
    const case2 = historyIncludesBCD && currentOnlyA;

    return case1 || case2;
  }

  /**
   * 检查第18题学历降低互斥
   */
  _checkEducationDegreeConflict(item, result) {
    const { inProperty } = this.tkFlowInfo();
    const { oldPaperAnswer } = inProperty;

    // 检查是否有历史答案
    if (!oldPaperAnswer) {
      return;
    }

    // 解析历史答案，获取第18题的历史选择
    const historyAnswers = this._parseHistoryAnswers(oldPaperAnswer);
    const question18History = historyAnswers['1030']; // 第18题题号

    if (!question18History || question18History.length === 0) {
      return; // 没有第18题的历史答案
    }

    // 获取历史学历等级（题目选项对应学历等级）
    const historyDegreeLevel = this._getDegreeLevelFromAnswer(
      question18History[0]
    );

    // 获取当前选择的学历等级（从当前选择的答案转换）
    const currentDegreeLevel = this._getDegreeLevelFromAnswer(item.answerNo);

    // 检查学历是否降低（数值越大学历越低）
    if (
      historyDegreeLevel &&
      currentDegreeLevel &&
      currentDegreeLevel > historyDegreeLevel
    ) {
      result.hasConflict = true;
      result.conflictType = '01'; // 非强制互斥
      result.conflictList.push({
        questionIndex1: 18,
        questionIndex2: '',
        questionContent1: item.questionContent,
        questionContent2: '',
        tips: '您的最高学历较上次测评结果有降低，请再次确认信息真实准确或进行修改。'
      });
    }
  }

  /**
   * 根据题目答案选项获取学历等级
   * A=1(博士), B=2(硕士), C=3(本科), D=4(大专), E=5(中专), F=6(高中), G=7(初中及以下)
   */
  _getDegreeLevelFromAnswer(answerNo) {
    const answerToLevel = {
      1: 1, // A. 博士
      2: 2, // B. 硕士
      3: 3, // C. 本科
      4: 4, // D. 大专
      5: 5, // E. 中专
      6: 6, // F. 高中
      7: 7 // G. 初中及以下
    };
    return answerToLevel[answerNo] || null;
  }

  /**
   * 处理强制互斥逻辑
   */
  handleForcedExclusion() {
    // 使用重新评估的方式来处理所有强制互斥
    this._reevaluateAllForcedExclusions();
  }

  /**
   * 禁用冲突选项
   */
  _disableConflictOptions(conflictQuestionList) {
    this.questionList.forEach((quesArr) => {
      const questionNo = quesArr[0].questionNo;
      conflictQuestionList.forEach((conflictQuestion) => {
        if (conflictQuestion.questionId === questionNo) {
          quesArr.forEach((questionItem) => {
            if (conflictQuestion.answerIds.includes(questionItem.answerNo)) {
              this.$set(questionItem, 'disabled', true);
            }
          });
        }
      });
    });
  }

  /**
   * 重新评估所有强制互斥状态
   */
  _reevaluateAllForcedExclusions() {
    // 首先清除所有禁用状态
    this.questionList.forEach((quesArr) => {
      quesArr.forEach((questionItem) => {
        this.$set(questionItem, 'disabled', false);
      });
    });

    // 然后重新应用所有当前选中选项的强制互斥规则
    this.questionList.forEach((quesArr) => {
      quesArr.forEach((questionItem) => {
        if (questionItem.checked) {
          // 检查这个选项是否触发强制互斥
          ruleList.forEach((ruleItem) => {
            if (
              questionItem.questionNo === ruleItem.questionId &&
              ruleItem.answerIdList.includes(questionItem.answerNo) &&
              ruleItem.ruleNo === '02'
            ) {
              this._disableConflictOptions(ruleItem.questionList);
            }
          });
        }
      });
    });
  }

  /**
   * 获取题目提示信息
   * @param {string} questionNo - 题目编号
   * @returns {string} 提示信息
   */
  getQuestionTips(questionNo) {
    // 检查所有强制互斥规则，找出被屏蔽的原因
    const conflictingQuestions = this._findConflictingQuestions(questionNo);

    if (conflictingQuestions.length > 0) {
      const rultType02 = conflictingQuestions.some(
        ({ ruleType }) => ruleType === '02'
      );
      if (rultType02) {
        return `根据您的选择，已屏蔽矛盾选项，如需调整可取消后重新选择。`;
      }
      const questionNumbers = conflictingQuestions
        .map((q) => q.questionIndex)
        .join('/');
      return `根据第${questionNumbers}题的选择，已屏蔽矛盾选项，如需调整可重新选择。`;
    }

    return '';
  }

  /**
   * 查找导致当前题目选项被屏蔽的题目
   * @param {string} questionNo - 当前题目编号
   * @returns {Array} 冲突题目信息数组
   */
  _findConflictingQuestions(questionNo) {
    const conflictingQuestions = [];

    // 遍历所有强制互斥规则
    ruleList.forEach((ruleItem) => {
      if (ruleItem.ruleNo === '02') {
        // 检查当前题目是否在互斥目标中
        ruleItem.questionList.forEach((targetQuestion) => {
          if (targetQuestion.questionId === questionNo) {
            // 检查触发互斥的题目是否有选中的选项
            const triggerQuestion = this.questionList.find(
              (q) => q[0].questionNo === ruleItem.questionId
            );

            if (triggerQuestion) {
              const selectedOption = triggerQuestion.find(
                (option) =>
                  ruleItem.answerIdList.includes(option.answerNo) &&
                  option.checked
              );

              if (selectedOption) {
                // 找到题目索引
                const questionIndex =
                  this.questionList.findIndex(
                    (q) => q[0].questionNo === ruleItem.questionId
                  ) + 1;

                conflictingQuestions.push({
                  questionIndex,
                  questionNo: ruleItem.questionId,
                  selectedOption,
                  ruleType: ruleItem.ruleType
                });
              }
            }
          }
        });
      }
    });

    return conflictingQuestions;
  }

  /**
   * 生成冲突提示文案
   * @param {Object} context - 上下文信息
   * @returns {string} 提示文案
   */
  _generateConflictTips(context) {
    const {
      currentItem,
      currentQuestionIndex,
      conflictQuestionIndex,
      conflictQuestionContent,
      ruleItem,
      targetQuestionId
    } = context;

    // 第12题和第13题的个性化提示
    if (currentItem.questionNo === '1027' && targetQuestionId === '1028') {
      return '您重点参与的投资品种（第12题）可能与您的投资需求（第13题）不符，请确认勾选的信息准确无误。';
    } else if (
      currentItem.questionNo === '1028' &&
      targetQuestionId === '1027'
    ) {
      return '您的投资需求（第13题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。';
    }

    // 通用提示逻辑
    if (ruleItem.ruleNo === '01') {
      if (ruleItem.ruleType === '00') {
        return `${currentItem.questionContent}（第${currentQuestionIndex}题）与${conflictQuestionContent}（第${conflictQuestionIndex}题）可能不符，请确认勾选的信息准确无误。`;
      } else {
        return `您的选项与第${conflictQuestionIndex}题选项矛盾，请确认或重新选择。`;
      }
    } else {
      return `${currentItem.questionContent}（第${currentQuestionIndex}题）与第${conflictQuestionIndex}题的选项可能不符，请确认勾选的信息准确无误`;
    }
  }
}
