<template>
  <fragment>
    <div class="acct_status_item" v-show="fundAccountList.length > 0">
      <div class="xh_tit">
        <div class="wrap">
          <h5>资金账户</h5>
          <span
            @click="
              switchBtnItem(
                mianFundAccount,
                `${mianFundAccount.type}++${mianFundAccount.account}`
              )
            "
            :class="{ checked: fundAccAll }"
            class="icon_check"
          ></span>
        </div>
      </div>
      <ul class="acct_list xh">
        <li v-for="(item, i) in fundAccountList" :key="i">
          <div
            class="icon_check"
            :class="{
              checked: item.chooseFlag === 1,
              disabled: item.blamer.length
            }"
            @click="switchBtnItem(item)"
          >
            <span>{{ item.name }}：{{ item.account }}</span
            ><!-- <em class="acct_s_tag">正常</em> -->
          </div>
          <div v-show="item.chooseFlag === 1" class="imp_c_tips no_icon">
            <p class="warn">
              <span class="imp"
                >若您的账户有资金余额，可在提交销户的同时办理利息结算，结息后的所有资金将转出到您账户对应的三方存管银行中</span
              >
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="unifiedAccountList.length > 0">
      <div class="xh_tit">
        <div class="wrap">
          <h5>一码通账户</h5>
          <span
            @click="switchBtnItem({ type: 0 }, '0-all')"
            :class="{ checked: unifiedAccAll }"
            class="icon_check"
          ></span>
        </div>
      </div>
      <ul class="acct_list xh">
        <li v-for="(item, i) in unifiedAccountList" :key="i">
          <div
            class="icon_check"
            :class="{
              checked: item.chooseFlag === 1,
              disabled: item.blamer.length
            }"
            @click="switchBtnItem(item)"
          >
            <span>{{ item.name }}：{{ item.account }}</span
            ><!-- <em class="acct_s_tag">正常</em> -->
          </div>
          <div v-show="item.chooseFlag === 1" class="imp_c_tips no_icon">
            <p class="warn">
              <span class="imp"
                >注销后您名下所有账号都将同步注销，且注销后新开交易经验需重新计算</span
              >
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="stockholderList.length > 0">
      <div class="xh_tit">
        <div class="wrap">
          <h5>证券账户</h5>
          <span
            @click="switchBtnItem({ type: 2 }, '2-all')"
            :class="{ checked: stockholderAll, disabled: stockholderAllDis }"
            class="icon_check"
          ></span>
        </div>
      </div>
      <ul class="acct_list xh">
        <li v-for="(item, i) in stockholderList" :key="i">
          <div
            class="icon_check"
            :class="{
              checked: item.chooseFlag === 1,
              disabled: item.blamer.length
            }"
            @click="switchBtnItem(item, `${item.type}++${item.account}`, [])"
          >
            <span>{{ item.name }}：{{ item.account }}</span
            ><!-- <em class="acct_s_tag">正常</em> -->
          </div>
          <div
            v-if="
              item.accountType === enumAccountType.huA ||
              item.accountType === enumAccountType.huB ||
              item.accountType === enumAccountType.huFund
            "
            class="xh_switchlist"
          >
            <div
              v-if="item.allowOperationType.includes('3')"
              :class="{ active: item.checkOperationType.includes('3') }"
              class="item"
            >
              <span
                @click="
                  switchBtnItem(item, `${item.type}+3+${item.account}`, '3')
                "
                >撤挂(含撤指定)</span
              >
            </div>
            <div
              v-if="item.allowOperationType.includes('5')"
              :class="{ active: item.checkOperationType.includes('5') }"
              class="item"
            >
              <span
                @click="
                  switchBtnItem(item, `${item.type}+5+${item.account}`, '5')
                "
                >中登注销</span
              >
            </div>
          </div>
          <div
            v-else-if="
              item.accountType === enumAccountType.shenA ||
              item.accountType === enumAccountType.shenB ||
              item.accountType === enumAccountType.shenFund
            "
            class="xh_switchlist"
          >
            <div
              v-if="item.allowOperationType.includes('4')"
              :class="{ active: item.checkOperationType.includes('4') }"
              class="item"
            >
              <span
                @click="
                  switchBtnItem(item, `${item.type}+4+${item.account}`, '4')
                "
                >撤挂</span
              >
            </div>
            <div
              v-if="item.allowOperationType.includes('5')"
              :class="{ active: item.checkOperationType.includes('5') }"
              class="item"
            >
              <span
                @click="
                  switchBtnItem(item, `${item.type}+5+${item.account}`, '5')
                "
                >中登注销</span
              >
            </div>
          </div>
          <div
            v-if="item.checkOperationType.includes('5')"
            v-show="item.chooseFlag === 1"
            class="imp_c_tips no_icon"
          >
            <p class="warn">
              <span class="imp"
                >注销后将不能在任何证券公司使用，在我司的相关交易权限将同步注销</span
              >
            </p>
          </div>
          <div
            v-else-if="item.checkOperationType.includes('3')"
            v-show="item.chooseFlag === 1"
            class="imp_c_tips no_icon"
          >
            <p class="warn">
              <span class="imp"
                >不注销账户，但撤挂(含撤指定)后无法在我司继续使用</span
              >
            </p>
          </div>
          <div
            v-else-if="item.checkOperationType.includes('4')"
            v-show="item.chooseFlag === 1"
            class="imp_c_tips no_icon"
          >
            <p class="warn">
              <span class="imp">不注销账户，但撤挂后无法在我司继续使用</span>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="financialAccList.length > 0">
      <div class="xh_tit">
        <div class="wrap">
          <h5>理财账户</h5>
          <span
            @click="switchBtnItem({ type: 5 }, '5-all')"
            :class="{ checked: financialAccAll, disabled: financialAccAllDis }"
            class="icon_check"
          ></span>
        </div>
      </div>
      <ul class="acct_list xh">
        <li v-for="(item, i) in financialAccList" :key="i">
          <div
            class="icon_check"
            :class="{
              checked: item.chooseFlag === 1,
              disabled: item.blamer.length
            }"
            @click="switchBtnItem(item)"
          >
            <span>{{ item.name }}：{{ item.account }}</span
            ><!-- <em class="acct_s_tag">正常</em> -->
          </div>
          <div v-show="item.chooseFlag === 1" class="imp_c_tips no_icon">
            <p class="warn">
              <span class="imp"
                >注销理财账户的时效应以注册登记机构确认为准</span
              >
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="creditAccList.length > 0">
      <div class="xh_tit">
        <div class="wrap">
          <h5>信用账户</h5>
          <span
            @click="switchBtnItem({ type: 3 }, '3-all')"
            :class="{ checked: creditAccAll, disabled: creditAccAllDis }"
            class="icon_check"
          ></span>
        </div>
      </div>
      <ul class="acct_list xh">
        <li v-for="(item, i) in creditAccList" :key="i">
          <div
            class="icon_check"
            :class="{
              checked: item.chooseFlag === 1,
              disabled: item.blamer.length
            }"
            @click="switchBtnItem(item)"
          >
            <span>{{ item.name }}：{{ item.account }}</span
            ><!-- <em class="acct_s_tag">正常</em> -->
          </div>
          <div v-show="item.chooseFlag === 1" class="imp_c_tips no_icon">
            <p class="warn">
              <span class="imp"
                >注销后将不能在任何证券公司使用，在我司的相关交易权限将同步注销</span
              >
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="acct_status_item" v-show="optionAccList.length > 0">
      <div class="xh_tit">
        <div class="wrap">
          <h5>衍生品账户</h5>
          <span
            @click="switchBtnItem({ type: 4 }, '4-all')"
            :class="{ checked: optionAccAll, disabled: optionAccAllDis }"
            class="icon_check"
          ></span>
        </div>
      </div>
      <ul class="acct_list xh">
        <li v-for="(item, i) in optionAccList" :key="i">
          <div
            class="icon_check"
            :class="{
              checked: item.chooseFlag === 1,
              disabled: item.blamer.length
            }"
            @click="switchBtnItem(item)"
          >
            <span>{{ item.name }}：{{ item.account }}</span
            ><!-- <em class="acct_s_tag">正常</em> -->
          </div>
          <div v-show="item.chooseFlag === 1" class="imp_c_tips no_icon">
            <p class="warn">
              <span class="imp"
                >注销后将不能在任何证券公司使用，在我司的相关交易权限将同步注销</span
              >
            </p>
          </div>
        </li>
      </ul>
    </div>
  </fragment>
</template>

<script>
import {
  queryOfAccountClosureList,
  recordOfAccountClosure,
  customerRetainLayeredDataQry
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
export default {
  name: 'ReserveCloseAccountSelector',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      unifiedAccountList: [], // 一码通账户列表
      fundAccountList: [], //资金账户列表
      financialAccList: [], //理财账户列表
      creditAccList: [], //信用账户列表
      optionAccList: [], //衍生品账户列表
      stockholderList: [], //证券账户列表
      enumAccountType: {
        // type = 0
        ymtAccount: 'YMT', // 一码通账号
        // type = 1
        mainFundAccount: 'C', // 主资金账号
        assistFundAccount: 'CF0M0', // 辅资金账号
        creditFundAccount: 'CF7M1', // 信用资金账号
        optionFundAccount: 'CFBM1', // 衍生品资金账号
        // type = 2
        huA: 'CF0M1S0E1', // 沪A
        huB: 'CF0M1S0ED', // 沪B
        huFund: 'CF0M1S1E1', // 沪基金
        shenA: 'CF0M1S0E2', // 深A
        shenB: 'CF0M1S0EH', // 深B
        shenFund: 'CF0M1S1E2', // 深基金
        shenHK: 'CF0M1S0ES', // 深HK
        // type = 3
        creditHuA: 'CF7M1S0E1', // 信用沪A
        creditShenA: 'CF7M1S0E2', // 信用深A
        // type = 4
        optionHuA: 'CFBM1S0E1', // 衍生品沪A
        optionShenA: 'CFBM1S0E2', // 衍生品深A
        // type = 5
        financial: 'CF0M1O' // 理财账户
      },
      conclusion: '' // 是否挽留
    };
  },
  watch: {
    btnOkStatus: {
      handler(nv) {
        if (nv) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submitInfo
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        }
      },
      immediate: true
    }
  },
  computed: {
    mianFundAccount() {
      return this.fundAccountList.find(
        (s) => s.accountType == this.enumAccountType.mainFundAccount
      );
    },
    // 一码通是否全选
    unifiedAccAll() {
      return this.unifiedAccountList.every((s) => s.chooseFlag == 1);
    },
    // 一码通是否勾选
    unifiedAccChecked() {
      return this.unifiedAccountList.some((s) => s.chooseFlag == 1);
    },
    // 资金账号是否全选
    fundAccAll() {
      return this.fundAccountList.every((s) => s.chooseFlag == 1);
    },
    // 资金账号是否勾选
    fundAccChecked() {
      return this.fundAccountList.some((s) => s.chooseFlag == 1);
    },
    // 理财账户是否全选
    financialAccAll() {
      return this.financialAccList.every((s) => s.chooseFlag == 1);
    },
    // 理财账户是否勾选
    financialAccChecked() {
      return this.financialAccList.some((s) => s.chooseFlag == 1);
    },
    // 理财账户是否需要全部禁用
    financialAccAllDis() {
      return this.financialAccList.every(
        (s) =>
          s.blamer.length && !(s.blamer.length === 1 && s.blamer[0] == '5-all')
      );
    },
    // 信用账户是否全选
    creditAccAll() {
      return this.creditAccList.every((s) => s.chooseFlag == 1);
    },
    // 信用账户是否勾选
    creditAccChecked() {
      return this.creditAccList.some((s) => s.chooseFlag == 1);
    },
    // 信用账户是否需要全部禁用
    creditAccAllDis() {
      return this.creditAccList.every(
        (s) =>
          s.blamer.length && !(s.blamer.length === 1 && s.blamer[0] == '3-all')
      );
    },
    // 衍生品账户是否全选
    optionAccAll() {
      return this.optionAccList.every((s) => s.chooseFlag == 1);
    },
    // 衍生品账户是否勾选
    optionAccChecked() {
      return this.optionAccList.some((s) => s.chooseFlag == 1);
    },
    // 衍生品账户是否需要全部禁用
    optionAccAllDis() {
      return this.optionAccList.every(
        (s) =>
          s.blamer.length && !(s.blamer.length === 1 && s.blamer[0] == '4-all')
      );
    },
    // 证券账户是否全选
    stockholderAll() {
      return this.stockholderList.every((s) => s.chooseFlag == 1);
    },
    // 证券账户是否勾选
    stockholderChecked() {
      return this.stockholderList.some((s) => s.chooseFlag == 1);
    },
    // 证券账户是否已经有一个操作类型已勾选
    stockholderOneChecked() {
      return this.stockholderList.some(
        (s) => s.chooseFlag == 1 || s.checkOperationType.length
      );
    },
    // 证券账户是否需要全部禁用
    stockholderAllDis() {
      return this.stockholderList.every(
        (s) =>
          s.blamer.length && !(s.blamer.length === 1 && s.blamer[0] == '2-all')
      );
    },
    btnOkStatus() {
      return (
        this.unifiedAccChecked ||
        this.fundAccChecked ||
        this.financialAccChecked ||
        this.creditAccChecked ||
        this.optionAccChecked ||
        this.stockholderChecked
      );
    }
  },
  created() {
    this.customerRetainLayeredDataQry();
  },
  mounted() {
    this.$nextTick(() => {
      this.renderingView();
    });
  },
  methods: {
    customerRetainLayeredDataQry() {
      let { clientId } = this.$store.state.user?.userInfo;
      customerRetainLayeredDataQry({ clientId })
        .then(({ data }) => {
          this.conclusion = data?.conclusion ?? '';
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    renderingView() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      const preFlowInsId = this.$attrs.preFlowInsId;
      const { bizType } = this.tkFlowInfo().inProperty;
      queryOfAccountClosureList({ flowToken, preFlowInsId, bizType })
        .then(({ data }) => {
          // console.log(data)
          // 账号类别：0.一码通 1.资金账号 2.证券账户 3.信用证券账户 4.衍生品证券账户（衍生） 5.理财账户
          for (const item of data) {
            const { type } = item;
            item.chooseFlag = 0;
            item.backAllowOperationType = item.allowOperationType;
            item.allowOperationType = item.allowOperationType?.split(',') || []; // 3 撤指定  4 撤挂  5 中登注销  6 转托管
            // huA huB huFund 去掉 4
            if (
              item.accountType == this.enumAccountType.huA ||
              item.accountType == this.enumAccountType.huB ||
              item.accountType == this.enumAccountType.huFund
            ) {
              item.allowOperationType = item.allowOperationType.filter(
                (i) => i != '4'
              );
            }
            // shenA shenB shenFund 去掉 6
            if (
              item.accountType == this.enumAccountType.shenA ||
              item.accountType == this.enumAccountType.shenB ||
              item.accountType == this.enumAccountType.shenFund
            ) {
              item.allowOperationType = item.allowOperationType.filter(
                (i) => i != '6'
              );
            }
            item.checkOperationType = [];
            item.blamer = []; // 记录是谁让按钮禁用了
            // item.isDisableZD = []; // 是否需要禁用中登注销
            if (type === '0') {
              this.unifiedAccountList.push({ ...item });
            } else if (type === '1') {
              this.fundAccountList.push({ ...item });
            } else if (type === '2') {
              this.stockholderList.push({ ...item });
            } else if (type === '3') {
              this.creditAccList.push({ ...item });
            } else if (type === '4') {
              this.optionAccList.push({ ...item });
            } else if (type === '5') {
              this.financialAccList.push({ ...item });
            }
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    /**
     * 按钮点击事件 -- 含弹窗提示
     * @param {*} item 渲染对象
     * @param {*} target 触发勾选行为的对象，格式 item.type++item.account
     * @param {*} operationType 需要勾选的操作类型
     */
    switchBtnItem(
      item = {},
      target = `${item.type}++${item.account}`,
      operationType = item.allowOperationType
    ) {
      const blamer = target == `${item.type}++${item.account}` ? null : target;
      // 账号类别：0.一码通 1.资金账号 2.证券账户 3.信用证券账户 4.衍生品证券账户（衍生） 5.理财账户
      if (item.type == 0) {
        const isAll = target == '0-all';
        const confirmBack = (chooseFlag) => {
          if (isAll) {
            this.unifiedAccountList.forEach((account) => {
              this.onceCheckedItem(
                account,
                target,
                account.allowOperationType,
                chooseFlag
              );
            });
          } else {
            this.onceCheckedItem(item, null, operationType, chooseFlag);
          }
          this.filterAccount(
            blamer ? this.unifiedAccountList : [item],
            target,
            true,
            chooseFlag
          );
        };
        // 判断是否需要单一码通可选的弹窗提示
        if (
          this.unifiedAccountList.length > 1 &&
          (isAll ||
            (item.chooseFlag === 0 &&
              this.unifiedAccountList.some((t) => t.chooseFlag == '1')))
        ) {
          this.$TAlert({
            title: '温馨提示',
            tips: '1次只能勾选1个一码通账号，如有多个一码通账号，请分次提交。',
            hasCancel: false,
            confirmBtn: '我知道了',
            confirm: () => {}
          });
          return true;
        } else if (item.chooseFlag === 0) {
          // 如果已有勾选的，不需要弹窗提示，直接勾选
          if (this.unifiedAccChecked) {
            confirmBack(1);
          } else {
            this.$TAlert({
              title: '温馨提示',
              tips: '注销一码通，如有证券账户、衍生品账户、信用账户将同时注销，请确认是否要注销',
              hasCancel: true,
              confirmBtn: '确定',
              cancelBtn: '取消',
              confirm: () => {
                confirmBack(1);
              },
              cancel: () => {}
            });
          }
        } else if (item.chooseFlag === 1) {
          confirmBack(0);
        }
      } else if (item.type == 1) {
        const isAll =
          target ==
          `${this.mianFundAccount.type}++${this.mianFundAccount.account}`;
        if (item.chooseFlag === 0 || (isAll && !this.fundAccAll)) {
          // 主资金账号 C
          if (item.accountType == this.enumAccountType.mainFundAccount) {
            const confirm = () => {
              if (
                isAll ||
                item.accountType == this.enumAccountType.mainFundAccount
              ) {
                this.fundAccountList.forEach((account) => {
                  // 主资金账号不需要禁用逻辑
                  const _blamer =
                    target == `${account.type}++${account.account}`
                      ? null
                      : target;
                  this.onceCheckedItem(
                    account,
                    _blamer,
                    account.allowOperationType,
                    1
                  );
                });
              } else {
                this.onceCheckedItem(item, null, operationType, 1);
              }
              this.filterAccount(
                blamer ? this.fundAccountList : [item],
                target,
                false,
                1
              );
            };
            // // 如果已有勾选的，不需要弹窗提示，直接勾选
            // if (this.fundAccChecked) {
            //   confirm();
            // } else {
            this.$TAlert({
              title: '温馨提示',
              tips: '注销主资金账户，如有辅资金账户、证券账户、理财账户、衍生品账户、信用账户将同时注销，请确认',
              hasCancel: true,
              confirmBtn: '确定',
              cancelBtn: '取消',
              confirm,
              cancel: () => {}
            });
            // }
          }
          // 信用资金账号 CF7M1
          else if (item.accountType == this.enumAccountType.creditFundAccount) {
            const confirm = () => {
              this.onceCheckedItem(item, null, operationType, 1);
              this.filterAccount(
                blamer ? this.fundAccountList : [item],
                target,
                false,
                1
              );
            };
            // // 如果已有勾选的，不需要弹窗提示，直接勾选
            // if (this.creditAccChecked) {
            //   confirm();
            // } else {
            this.$TAlert({
              title: '温馨提示',
              tips: '注销信用资金账户，如有信用证券账户将同时注销，请确认',
              hasCancel: true,
              confirmBtn: '确定',
              cancelBtn: '取消',
              confirm,
              cancel: () => {}
            });
            // }
          }
          // 衍生品资金账号 CFBM1
          else if (item.accountType == this.enumAccountType.optionFundAccount) {
            const confirm = () => {
              this.onceCheckedItem(item, null, operationType, 1);
              this.filterAccount(
                blamer ? this.fundAccountList : [item],
                target,
                false,
                1
              );
            };
            // // 如果已有勾选的，不需要弹窗提示，直接勾选
            // if (this.optionAccChecked) {
            //   confirm();
            // } else {
            this.$TAlert({
              title: '温馨提示',
              tips: '注销衍生品资金账户，如有衍生品合约账户将同时注销，请确认',
              hasCancel: true,
              confirmBtn: '确定',
              cancelBtn: '取消',
              confirm,
              cancel: () => {}
            });
            // }
          } else {
            this.onceCheckedItem(item, null, operationType, 1);
          }
        } else {
          if (
            isAll ||
            item.accountType == this.enumAccountType.mainFundAccount
          ) {
            this.fundAccountList.forEach((account) => {
              // 主资金账号不需要禁用逻辑
              const _blamer =
                target == `${account.type}++${account.account}` ? null : target;
              this.onceCheckedItem(
                account,
                _blamer,
                account.allowOperationType,
                0
              );
            });
          } else {
            this.onceCheckedItem(item, null, operationType, 0);
          }
          this.filterAccount(
            blamer ? this.fundAccountList : [item],
            target,
            false,
            0
          );
        }
      } else if (item.type == 2) {
        if (target == '2-all') {
          const confirm = () => {
            const chooseFlag = !this.stockholderAll;
            this.stockholderList.forEach((account) => {
              this.onceCheckedItem(account, blamer, [], chooseFlag);
            });
            this.filterAccount([item], blamer, false, chooseFlag);
          };

          if (!this.stockholderChecked) {
            this.$TAlert({
              title: '温馨提示',
              tips: '注销证券账户，如有同市场信用证券账户/衍生品合约账户将同时注销，请确认',
              hasCancel: true,
              confirmBtn: '确定',
              cancelBtn: '取消',
              confirm,
              cancel: () => {}
            });
          } else {
            confirm();
          }
          return true;
        }
        if (item.chooseFlag === 0) {
          const confirm = () => {
            this.onceCheckedItem(item, blamer, operationType, 1);
            this.filterAccount([item], target, false, 1);
          };
          // 如果已有勾选的，不需要弹窗提示，直接勾选
          if (this.stockholderOneChecked) {
            confirm();
            return true;
          }
          // 沪A CF0M1S0E1
          if (item.checkOperationType?.length === 0) {
            this.$TAlert({
              title: '温馨提示',
              tips: '注销证券账户，如有同市场信用证券账户/衍生品合约账户将同时注销，请确认',
              hasCancel: true,
              confirmBtn: '确定',
              cancelBtn: '取消',
              confirm,
              cancel: () => {}
            });
          } else {
            this.onceCheckedItem(item, blamer, operationType, 1);
          }
        } else {
          let chooseFlag = 0;
          if (Array.isArray(operationType)) {
            this.onceCheckedItem(item, blamer, operationType, chooseFlag);
          } else {
            chooseFlag = !item.checkOperationType.includes(operationType)
              ? 1
              : 0;
            this.onceCheckedItem(item, blamer, operationType, chooseFlag);
          }
          // // 兼容 证券账户全选的情况下，点击深A的中登注销按钮      ！！！冗余逻辑，可以让产品改需求来去除  --已去除
          // this.$nextTick(() => {
          //   if (isZD && !this.stockholderAllDis && [this.enumAccountType.shenA, this.enumAccountType.shenB, this.enumAccountType.shenFund].includes(item.accountType)) {
          //     this.stockholderList.forEach((account) => {
          //       // if 沪市  else 深市
          //       if (account.isDisableZD?.length) {
          //         account.blamer = account.blamer.filter(i => i != '2-all').length ? account.blamer.filter(i => i != '2-all') : [`${account.type}++${account.account}`];
          //         account.isDisableZD = account.isDisableZD.filter(i => i != '2-all');
          //       } else {
          //         let _blamer = account.blamer.filter(i => i != '2-all');
          //         if (account.checkOperationType.includes('5')) {
          //           _blamer = account.blamer.map(i => i == '2-all' ? `${account.type}++${account.account}` : i);
          //         }
          //         account.blamer = _blamer;
          //       }
          //     })

          //     this.creditAccList.forEach((account) => {
          //       if (account.accountType == this.enumAccountType.creditHuA) {
          //         const hu = this.stockholderList.find(i => i.accountType == this.enumAccountType.huA);
          //         account.blamer = account.blamer.filter(i => i != '2-all').length ? account.blamer.filter(i => i != '2-all') : [`${hu.type}++${hu.account}`];
          //       }
          //       if (account.accountType == this.enumAccountType.creditShenA) {
          //         const shen = this.stockholderList.find(i => i.accountType == this.enumAccountType.shenA);
          //         account.blamer = account.blamer.filter(i => i != '2-all').length ? account.blamer.filter(i => i != '2-all') : [`${shen.type}++${shen.account}`];
          //       }
          //     })

          //     this.optionAccList.forEach((account) => {
          //       if (account.accountType == this.enumAccountType.optionHuA) {
          //         const hu = this.stockholderList.find(i => i.accountType == this.enumAccountType.huA);
          //         account.blamer = account.blamer.filter(i => i != '2-all').length ? account.blamer.filter(i => i != '2-all') : [`${hu.type}++${hu.account}`];
          //       }
          //       if (account.accountType == this.enumAccountType.optionShenA) {
          //         const shen = this.stockholderList.find(i => i.accountType == this.enumAccountType.shenA);
          //         account.blamer = account.blamer.filter(i => i != '2-all').length ? account.blamer.filter(i => i != '2-all') : [`${shen.type}++${shen.account}`];
          //       }
          //     })
          //   }
          // })
          // item.chooseFlag 已勾选 再次进来是中登注销的，不联动
          if (Array.isArray(operationType)) {
            this.filterAccount([item], target, false, chooseFlag);
          }
        }
      } else if (item.type == 3) {
        const isAll = target == '3-all';
        const chooseFlag = !this.creditAccAll;
        if (isAll) {
          this.creditAccList.forEach((account) => {
            this.onceCheckedItem(
              account,
              target,
              account.allowOperationType,
              chooseFlag
            );
          });
        } else {
          this.onceCheckedItem(item, null, operationType, !item.chooseFlag);
        }
      } else if (item.type == 4) {
        const isAll = target == '4-all';
        const chooseFlag = !this.optionAccAll;
        if (isAll) {
          this.optionAccList.forEach((account) => {
            this.onceCheckedItem(
              account,
              target,
              account.allowOperationType,
              chooseFlag
            );
          });
        } else {
          this.onceCheckedItem(item, null, operationType, !item.chooseFlag);
        }
      } else if (item.type == 5) {
        const isAll = target == '5-all';
        const chooseFlag = !this.financialAccAll;
        if (isAll) {
          this.financialAccList.forEach((account) => {
            this.onceCheckedItem(
              account,
              target,
              account.allowOperationType,
              chooseFlag
            );
          });
        } else {
          this.onceCheckedItem(item, null, operationType, !item.chooseFlag);
        }
      } else {
        this.onceCheckedItem(item, null, operationType, !item.chooseFlag);
      }
    },

    /**
     * 根据传入的账号信息，筛选到对应的账号，并勾选
     * @param {Array} parentAccList 需要勾选账号
     * @param {*} blamer 记录是谁触发的勾选
     * @param {Array} isOnceYMT 是否是一码通
     * @param {*} chooseFlag 需要修改到指定的勾选状态
     */
    filterAccount(parentAccList, blamer, isOnceYMT = false, chooseFlag) {
      console.log(
        'filterAccount-start',
        parentAccList,
        blamer,
        isOnceYMT,
        chooseFlag
      );
      // 资金账号
      const fundAccountType = [
        'assistFundAccount',
        'creditFundAccount',
        'optionFundAccount'
      ];
      // 证券账户
      const stackType = ['huA', 'huB', 'huFund', 'shenA', 'shenB', 'shenFund'];
      // 信用账户
      const creditType = ['creditHuA', 'creditShenA'];
      // 衍生品账户
      const optionType = ['optionHuA', 'optionShenA'];
      // 理财账户
      const financial = ['financial'];

      /**
       * 根据 typeList 识别 stockholderList creditAccList optionAccList financialAccList
       * @param {*} typeList
       * @param {*} isOnceYMT 是否是单一码通
       * @param {*} account 账号信息
       */
      const filterList = (typeList, account) => {
        for (let i = 0; i < typeList.length; i++) {
          const type = typeList[i];
          if (fundAccountType.includes(type)) {
          } else if (stackType.includes(type)) {
            for (const i in this.stockholderList) {
              if (Object.hasOwnProperty.call(this.stockholderList, i)) {
                const acc = this.stockholderList[i];
                // 单类型   单个一码通联动
                console.log(
                  'filterList-证券账户' + type,
                  this.enumAccountType[type],
                  acc.accountType,
                  isOnceYMT,
                  acc.acodeAccount,
                  account.account,
                  this.enumAccountType[type] == acc.accountType &&
                    ((isOnceYMT && acc.acodeAccount == account.account) ||
                      !isOnceYMT)
                );

                if (
                  this.enumAccountType[type] == acc.accountType &&
                  ((isOnceYMT && acc.acodeAccount == account.account) ||
                    !isOnceYMT)
                ) {
                  this.onceCheckedItem(
                    acc,
                    isOnceYMT ? `${account.type}++${account.account}` : blamer,
                    [],
                    chooseFlag
                  );
                }
              }
            }
          } else if (creditType.includes(type)) {
            for (const i in this.creditAccList) {
              if (Object.hasOwnProperty.call(this.creditAccList, i)) {
                const acc = this.creditAccList[i];
                // 单类型   单个一码通联动
                console.log(
                  'filterList-信用账户' + type,
                  this.enumAccountType[type],
                  acc.accountType,
                  isOnceYMT,
                  acc.acodeAccount,
                  account.account,
                  this.enumAccountType[type] == acc.accountType &&
                    ((isOnceYMT && acc.acodeAccount == account.account) ||
                      !isOnceYMT)
                );

                if (
                  this.enumAccountType[type] == acc.accountType &&
                  ((isOnceYMT && acc.acodeAccount == account.account) ||
                    !isOnceYMT)
                ) {
                  this.onceCheckedItem(
                    acc,
                    isOnceYMT ? `${account.type}++${account.account}` : blamer,
                    undefined,
                    chooseFlag
                  );
                }
              }
            }
          } else if (optionType.includes(type)) {
            for (const i in this.optionAccList) {
              if (Object.hasOwnProperty.call(this.optionAccList, i)) {
                const acc = this.optionAccList[i];
                // 单类型   单个一码通联动
                console.log(
                  'filterList-衍生品账户' + type,
                  this.enumAccountType[type],
                  acc.accountType,
                  isOnceYMT,
                  acc.acodeAccount,
                  account.account,
                  this.enumAccountType[type] == acc.accountType &&
                    ((isOnceYMT && acc.acodeAccount == account.account) ||
                      !isOnceYMT)
                );

                if (
                  this.enumAccountType[type] == acc.accountType &&
                  ((isOnceYMT && acc.acodeAccount == account.account) ||
                    !isOnceYMT)
                ) {
                  this.onceCheckedItem(
                    acc,
                    isOnceYMT ? `${account.type}++${account.account}` : blamer,
                    undefined,
                    chooseFlag
                  );
                }
              }
            }
          } else if (financial.includes(type)) {
            for (const i in this.financialAccList) {
              if (Object.hasOwnProperty.call(this.financialAccList, i)) {
                const acc = this.financialAccList[i];
                // 单类型   单个一码通联动
                console.log(
                  'filterList-理财账户' + type,
                  this.enumAccountType[type],
                  acc.accountType,
                  isOnceYMT,
                  acc.acodeAccount,
                  account.account,
                  this.enumAccountType[type] == acc.accountType &&
                    ((isOnceYMT && acc.acodeAccount == account.account) ||
                      !isOnceYMT)
                );

                if (
                  this.enumAccountType[type] == acc.accountType &&
                  ((isOnceYMT && acc.acodeAccount == account.account) ||
                    !isOnceYMT)
                ) {
                  this.onceCheckedItem(
                    acc,
                    isOnceYMT ? `${account.type}++${account.account}` : blamer,
                    undefined,
                    chooseFlag
                  );
                }
              }
            }
          }
        }
      };

      for (let i = 0; i < parentAccList.length; i++) {
        const parentAcc = parentAccList[i];
        if (parentAcc.type == '0') {
          const typeList = [...stackType, ...optionType, ...creditType];
          filterList(typeList, parentAcc);
        } else if (parentAcc.type == '1') {
          if (parentAcc.accountType == this.enumAccountType.mainFundAccount) {
            const typeList = [
              ...fundAccountType,
              ...stackType,
              ...creditType,
              ...optionType,
              ...financial
            ];
            filterList(typeList, parentAcc);
          } else if (
            parentAcc.accountType == this.enumAccountType.creditFundAccount
          ) {
            filterList(creditType, parentAcc);
          } else if (
            parentAcc.accountType == this.enumAccountType.optionFundAccount
          ) {
            filterList(optionType, parentAcc);
          }
        } else if (parentAcc.type == '2') {
          if (parentAcc.accountType == this.enumAccountType.huA) {
            const typeList = [optionType[0], creditType[0]];
            filterList(typeList, parentAcc);
          } else if (parentAcc.accountType == this.enumAccountType.shenA) {
            const typeList = [optionType[1], creditType[1]];
            filterList(typeList, parentAcc);
          } else {
            const typeList = [...optionType, ...creditType];
            filterList(typeList, parentAcc);
          }
        }
      }
    },

    /**
     * 单条渲染事件 -- 直接操作，不含弹窗提示
     * @param {*} item 渲染对象
     * @param {*} blamer 记录是谁触发的勾选
     * @param {*} operationType 需要勾选的操作类型
     * @param {*} chooseFlag 需要修改到指定的勾选状态
     */
    onceCheckedItem(
      item,
      blamer,
      operationType = item.allowOperationType,
      chooseFlag
    ) {
      let {
        checkOperationType,
        allowOperationType,
        blamer: itemBlamer,
        type,
        isDisableZD
      } = item;
      if (
        chooseFlag ||
        (chooseFlag &&
          JSON.stringify(operationType) !=
            JSON.stringify(item.allowOperationType))
      ) {
        blamer && !itemBlamer.includes(blamer) && itemBlamer.push(blamer);
        if (Array.isArray(operationType)) {
          checkOperationType.push(...operationType);
        } else {
          checkOperationType.push(operationType);
        }
        this.$set(item, 'checkOperationType', checkOperationType);
        this.$set(item, 'blamer', itemBlamer);
        item.chooseFlag = 1;
        return true;
      }

      // // 分别为 点击整条   点击撤指定/撤挂   点击中登注销   联动
      // // 第一种和第二种，都只操作 撤指定/撤挂，第三种需要同事操作 中登注销 和 撤指定/撤挂
      const _blamer = itemBlamer.filter((t) => {
        //   if (t?.startsWith('0++')) {
        //     const acc = t.replace('0++', '');
        //     for (let i = 0; i < this.unifiedAccountList.length; i++) {
        //       const sth = this.unifiedAccountList[i];
        //       if (sth.account == acc && sth.chooseFlag == 0) {
        //         return false;
        //       }
        //     }
        //   }
        //   if (t?.startsWith('1++')) {
        //     const acc = t.replace('1++', '');
        //     for (let i = 0; i < this.fundAccountList.length; i++) {
        //       const sth = this.fundAccountList[i];
        //       if (sth.account == acc && sth.chooseFlag == 0) {
        //         return false;
        //       }
        //     }
        //   }
        //   if (t?.startsWith('2++')) {
        //     const acc = t.replace('2++', '');
        //     for (let i = 0; i < this.stockholderList.length; i++) {
        //       const sth = this.stockholderList[i];
        //       if (sth.account == acc && sth.chooseFlag == 0) {
        //         return false;
        //       }
        //     }
        //   }
        //   // 一码通  资金账号  证券账户  全选，同步取消因为中登注销导致禁用的证券账户
        //   if ((blamer == '0-all' || blamer == `${this.mianFundAccount.type}++${this.mianFundAccount.account}` || blamer == '2-all')
        //     && t?.startsWith('2++')) {
        //     return false;
        //   }
        return t != blamer;
      });
      this.$set(item, 'blamer', _blamer);
      // this.$set(item, 'isDisableZD', isDisableZD.filter(t => t != blamer));

      let _checkOperationType = checkOperationType;
      // if (isZD && type == 2) {
      //   if (_blamer.length != 0) {
      //     if (!isCheckedZD || blamer?.startsWith('2++')) {
      //       _checkOperationType = checkOperationType.filter(t => t != '5');
      //     }
      //   } else if (blamer?.startsWith('2++')) {
      //     _checkOperationType = checkOperationType.filter(t => t != '5');
      //   } else {
      //     _checkOperationType = [];
      //   }
      //   this.$set(item, 'checkOperationType', _checkOperationType);
      // } else if (_blamer.length) {
      //   return true;
      // } else if (type != 2) {
      //   _checkOperationType = [];
      //   this.$set(item, 'checkOperationType', _checkOperationType);
      // } else {
      //   _checkOperationType = checkOperationType.filter(t => t != allowOperationType[0]);
      //   this.$set(item, 'checkOperationType', _checkOperationType);
      // }
      if (type == 2) {
        // 行复选  单个按钮
        if (_blamer.length === 0 && operationType.length === 0) {
          item.chooseFlag = 0;
        }
        if (operationType.length) {
          _checkOperationType = checkOperationType.filter(
            (t) => !operationType.includes(t)
          );
          this.$set(item, 'checkOperationType', _checkOperationType);
        }
      } else if (_blamer.length) {
        return true;
      } else {
        _checkOperationType = checkOperationType.filter(
          (t) => !operationType.includes(t)
        );
        this.$set(item, 'checkOperationType', _checkOperationType);
        if (_checkOperationType.length === 0) {
          item.chooseFlag = 0;
        }
      }
    },

    nextValidate() {
      // 点击下一步需判断联动勾选的证券账户是否有选择销声接作,如没有选择,则进行弹窗提示:请选择(沪A1111111)的销户操作
      let stockholderList = this.stockholderList.filter(
        (acc) => acc.chooseFlag == 1 && acc.checkOperationType.length === 0
      );
      if (stockholderList.length) {
        this.$TAlert({
          title: '温馨提示',
          tips: `请选择（${stockholderList
            .map((acc) => `${acc.name}${acc.account}`)
            .join(',')}）的销户操作`,
          confirmBtn: '我知道了',
          confirm: () => {}
        });
        return false;
      }

      // 再判断是否有证券账户，只选了销户操作，没有勾选
      stockholderList = this.stockholderList.filter(
        (acc) => acc.chooseFlag == 0 && acc.checkOperationType.length
      );
      if (stockholderList.length) {
        this.$TAlert({
          title: '温馨提示',
          tips: `请勾选（${stockholderList
            .map((acc) => `${acc.name}${acc.account}`)
            .join(',')}）`,
          confirmBtn: '我知道了',
          confirm: () => {}
        });
        return false;
      }
      return true;
    },

    submitInfo() {
      if (!this.nextValidate()) return false;

      const preFlowInsId = this.$attrs.preFlowInsId;
      const cancelAccountDataList = [
        ...this.unifiedAccountList,
        ...this.fundAccountList,
        ...this.stockholderList,
        ...this.creditAccList, //信用账户列表
        ...this.optionAccList, //衍生品账户列表
        ...this.financialAccList //理财账户列表
      ]
        .filter((acc) => acc.chooseFlag == 1)
        .map((obj) => {
          return Object.keys(obj)
            .filter((key) => key !== 'chooseFlag' && key !== 'blamer')
            .reduce((newObj, key) => {
              if (
                key == 'allowOperationType' ||
                key == 'backAllowOperationType'
              ) {
                newObj.allowOperationType = obj.backAllowOperationType;
              } else if (key == 'checkOperationType') {
                newObj.actionType = obj.checkOperationType.join(',');
              } else {
                newObj[key] = obj[key];
              }
              return newObj;
            }, {});
        });
      // console.log('cancelAccountDataList---' + JSON.stringify(cancelAccountDataList).length, cancelAccountDataList)

      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { branchNo, clientId } = this.$store.state.user?.userInfo;
      const { bizType } = this.tkFlowInfo().inProperty;
      recordOfAccountClosure({
        clientId,
        branchNo,
        bizType,
        flowToken,
        preFlowInsId,
        cancelAccountDataList: JSON.stringify(cancelAccountDataList),
        coverFlag: '1' // coverFlag  是否覆盖   0否 1是   默认不覆盖
      })
        .then(({ code, msg, data }) => {
          if (code === 0) {
            const accountNeedPwdList = data?.cancelAccountDataList.filter(
              (t) => t.needFundPassword == '1'
            );
            const bankNeedPwdList = data?.cancelAccountDataList.filter(
              (t) => t.needPassword == '1'
            );
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              accountNeedPwdList: accountNeedPwdList.length
                ? JSON.stringify(accountNeedPwdList)
                : '',
              bankNeedPwdList: bankNeedPwdList.length
                ? JSON.stringify(bankNeedPwdList)
                : '',
              conclusion: this.conclusion
            });
          } else {
            Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },

    // 证券账户，第一个按钮是否是中登
    isFirstBtnZD(item) {
      return item.allowOperationType?.[0] == '5';
    }
  }
};
</script>

<style scoped>
.imp_c_tips .imp {
  color: var(--impColor, #ff8533) !important;
}

.disabled {
  pointer-events: none;
}
</style>
