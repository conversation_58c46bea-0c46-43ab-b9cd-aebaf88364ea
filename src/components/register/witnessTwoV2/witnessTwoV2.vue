<template>
  <fragment>
    <!-- 原生双向 -->
    <section class="main fixed white_bg" style="position: fixed">
      <t-header title="双向见证" @back="back"></t-header>
      <article v-show="pageState == 1" class="content">
        <div class="video_tippage">
          <div class="title spel">
            <h3>将由见证人员与您进行视频连线</h3>
            <p>请做好以下准备</p>
          </div>
          <ul class="witness_list">
            <li>
              <i class="icon"></i>
              <p>光线充足的位置</p>
            </li>
            <li>
              <i class="icon"></i>
              <p>推荐使用WIFI网络</p>
            </li>
            <li>
              <i class="icon"></i>
              <p>周遭环境安静</p>
            </li>
            <li>
              <i class="icon"></i>
              <p>
                视频认证时间<em class="imp">{{ this.timeTips }}</em>
              </p>
            </li>
          </ul>
        </div>
      </article>
      <article v-show="pageState == 3" class="content">
        <div class="result_page">
          <div class="result_tips">
            <div class="icon vedio_ok"></div>
            <h5>恭喜! 视频已录制完成</h5>
            <p>您可以继续下一步</p>
          </div>
        </div>
      </article>
      <article v-show="pageState == 4" class="content">
        <div class="result_page">
          <div class="result_tips">
            <div class="icon vedio_error"></div>
            <h5>视频见证未通过</h5>
            <p>如有疑问请联系客服热线 95310</p>
          </div>
          <div class="reject_txtinfo">
            <h5 class="title">原因</h5>
            <p v-for="(item, index) in rejectReason" :key="index">
              {{ index + 1 }}、{{ item }}
            </p>
          </div>
        </div>
      </article>
      <footer v-if="pageState != 2" class="footer">
        <div class="ce_btn">
          <a v-if="pageState == 1" class="p_button" @click="checkWitnessTime"
            >开始认证</a
          >
          <a v-else-if="pageState == 3" class="p_button" @click="nextClick"
            >下一步</a
          >
          <a v-else-if="pageState == 4" class="p_button" @click="rejectNext">{{
            notNext ? '重新见证' : '重新提交'
          }}</a>
        </div>
      </footer>
    </section>

    <!-- H5双向，走ifream -->
    <iframe
      v-if="!isApp && startH5"
      style="
        position: fixed;
        width: 100%;
        height: 100vh;
        border: 0;
        top: 0;
        z-index: ********;
      "
      allow="microphone;camera;midi;encrypted-media;"
      ref="iframe"
      @load="childLoad"
      :src="childUrl"
    ></iframe>
  </fragment>
</template>

<script>
import {
  videoRegist,
  getVideoResult,
  flowQueryIns
} from '@/service/service.js';
import { addClientCritMark } from '@/service/service';
import { bkStockAccountWitnessTimeQry } from '@/service/shareholdAccountService';
import { EVENT_NAME } from '@/common/formEnum';
import { PROCESS_STATUS } from '@/common/enumeration';
import {
  isSupportWebRTC,
  compareVersions,
  getIOSVersion,
  compareVersionsCompare
} from '@/common/util';
export default {
  name: 'witnessTwoV2',
  inject: ['eventMessage', 'tkFlowInfo'],
  data() {
    return {
      startH5: false,
      pageState: 1, // 1准备页面  2排队（h5）  3成功页面  4失败页面
      regResult: {},
      rejectReason: [],
      notNext: '',

      childUrl: '',

      deviceSysVersion: '',
      devicePlatform: '',

      loadingEd: false,

      witnessPeriod: false,
      witnessTime: '',

      appId: ''
    };
  },
  props: {
    timeTips: {
      type: String,
      default: '交易日 9:00 ~ 17:00'
    },
    videoJson: {
      type: String,
      default: ''
    }
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    bkStockAccountWitnessTimeQry({
      flowToken: sessionStorage.getItem('TKFlowToken')
    }).then((res) => {
      // this.witnessPeriod = res.data.witnessPeriod;
      this.witnessTime = res.data.witnessTime;
      this.timeTips =
        '交易日 ' +
        this.witnessTime.split(',')[0] +
        ' ~ ' +
        this.witnessTime.split(',')[1];
    });

    const { inProperty } = this.tkFlowInfo();
    if (inProperty.videoJson !== '') {
      try {
        const { reg_flow_no } = JSON.parse(inProperty.videoJson);
        this.queryVideoResult(reg_flow_no);
      } catch (error) {
        console.log('解析视频见证JSON失败：', error);
      }
    }
    this.appId = inProperty.appId ? inProperty.appId : '';
    if (this.isApp) {
      const result = $h.callMessageNative({
        funcNo: '50001'
      });
      result.results = result.results || [];
      let data = result.results[0];
      if (result.error_no == '0' && data) {
        if (!$hvue.iBrowser.ios) {
          this.devicePlatform = data.devicePlatform;
          this.deviceSysVersion = 'Android ' + data.deviceSysVersion;
        } else {
          this.devicePlatform = data.devicePlatform;
          this.deviceSysVersion = 'iOS ' + data.deviceSysVersion;
        }
      }
      // this.toRegist();
    } else {
      // this.toRegist();
    }
    window.addEventListener(
      'message',
      (event) => {
        // 接收子页面回传的结果
        let result = event.data.authResult;
        // error_no -2 token过期 -1用户返回 0 通过 1驳回
        if (result.error_no === 0) {
          // 通过
          // this.pageState = 3;
          this.getVideoResult();
        } else if (result.error_no === 1) {
          this.getVideoResult();
        } else if (result.error_no === -1) {
          // 显示首页
          // this.eventMessage(this, EVENT_NAME.PREV_FLOW);
          this.startH5 = false;
        } else if (result.error_no === -2) {
          // 过期重新注册
        }
      },
      false
    );
  },

  methods: {
    // 以下为H5上传逻辑
    childLoad() {
      if (this.childUrl) {
        this.$refs.iframe.contentWindow.postMessage(
          {
            initParam: {
              flow_no: this.regResult.regFlowNo,
              client_id: this.$store.state.user.userInfo.fundAccount,
              cust_name: this.regResult.clientName,
              org_no: this.regResult.branchNo,
              biz_type: this.regResult.bizType,
              channel_no: this.appId,
              video_type: $hvue.customConfig.video.videoType,
              auth_type: 'authVideoWitness',
              authorization: this.regResult.jwtToken,
              main_color: '#fa443a',
              service_time: this.timeTips,
              no_pre_page: '1',
              no_cancel_queue_btn: '1',
              serviceInfoShowPosition: '1',
              bizFlowNo: this.$store.state.user.userInfo.fundAccount,
              no_title: '1'
            }
          },
          this.childUrl
        ); //子页面地址
      }
    },

    // 以下为原生上传逻辑
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },

    startTwoVideo() {
      window.videoCallBack = this.videoCallBack;
      let param = {
        userId: this.regResult.regFlowNo, // 用户编号
        userName: this.regResult.clientName, // 用户名称
        orgId: this.regResult.branchNo, // 营业部编号
        netWorkStatus: 'WIFI', // 网络状态 (可不填
        url:
          $hvue.customConfig.video.videoServer +
          '/wa-queue-server/servlet/json?', // 连接排队BUS的服务器地址 (ios必须要在url加上?不然无法识别
        moduleName: $hvue.customConfig.moduleName, // 必须为open
        funcNo: '60005', // 双向视频见证
        isRejectToH5: '1', //原生是否处理透明通道信息 默认为0
        isNewView: '0', // 是否使用vue版本界面
        isShowHeadRect: '0', // 是否视频见证中显示头像取景框方便客户对准	N	1：显示；其他都隐藏（目前4.0视频界面使用）
        version: '4.0', // 排队bus版本
        mainColor: '#fa443a', //视频见证主题颜色s
        // 统一视频兼容
        user_id: this.$store.state.user.userInfo.fundAccount,
        branch_id: this.regResult.branchNo,
        branchno: this.regResult.branchNo,
        user_name: this.regResult.clientName,
        client_id: this.$store.state.user.userInfo.fundAccount,
        client_name: this.regResult.clientName,
        channel_no: this.appId,
        channelNo: this.appId,
        channel: this.appId,
        appId: $hvue.customConfig.video.anychat.appId,
        origin: $hvue.platform,
        videoType: $hvue.customConfig.video.videoType,
        requestParam: `videoType=${$hvue.customConfig.video.videoType}`,
        biz_type: this.regResult.bizType,
        bizType: this.regResult.bizType,
        bizFlowNo: this.$store.state.user.userInfo.fundAccount,
        business_type: this.regResult.bizType,
        business_code: this.regResult.bizType,
        useTsyp: $hvue.customConfig.video.useTysp,
        serviceInfoShowPosition: '1',
        requestHeaders: {
          'tk-jwt-authorization': this.regResult.jwtToken
        },
        videoTipMsg: JSON.stringify({
          queueStaffOfflineMsg: '当前见证排队中',
          queueStaffOfflineSubMsg: '排队-人，预计-分钟接入，请您稍等',
          // 'queueLocationMsg':'',
          // 'queueLocationSubMsg':'',
          queueWaitAgreeMsg: '等待见证人员确认，请稍后...'
          // 'queueWaitAgreeSubMsg':'',
        }),
        serviceTipString: '见证人员'
      };
      console.log(param);
      let result = $h.callMessageNative(param);
      if (result.error_no !== '0') {
        console.log(result.error_info);
      }
    },

    checkWitnessTime() {
      bkStockAccountWitnessTimeQry({
        flowToken: sessionStorage.getItem('TKFlowToken')
      }).then((res) => {
        this.witnessPeriod = res.data.witnessPeriod;
        this.toRegistBefor();
      });
    },

    toRegistBefor() {
      if (!this.isApp) {
        let specialFlag = false;

        const ISIOS = /iphone|ipad/i.test(navigator.appVersion);
        const ISANDROID = /android/i.test(navigator.appVersion);

        const isXiaomiWechat = () => {
          // 参照ua-device.js
          let ua = navigator.userAgent;
          let isXiaomi = ua.match(/;\s(mi|m1|m2|m3|m4|hm)(\s*\w*)[\s\)]/i);
          if (ua.match(/(meitu|MediaPad)/i)) {
            // 排除美图手机meitu m4 mizhi
            isXiaomi = false;
          }
          if (/build\/HM\d{0,7}\)/i.test(ua) || ua.match(/redmi\s?(\d+)?/i)) {
            isXiaomi = true;
          }
          if (ua.match(/;\sshark/i)) {
            // 黑鲨手机
            isXiaomi = true;
          }
          let isAndroid =
            navigator.userAgent.toLowerCase().indexOf('android') > -1 ||
            navigator.userAgent.toLowerCase().indexOf('adr') > -1;
          let isWeChat =
            navigator.userAgent.toLocaleUpperCase().indexOf('MICROMESSENGER') >=
            0;
          return isXiaomi && isWeChat && isAndroid;
        };

        const specialIosInterval = [
          compareVersionsCompare(getIOSVersion(), '11.2.2', '>='),
          compareVersionsCompare(getIOSVersion(), '12.0.0', '<')
        ];

        // iOS版本使用H264推流会发生网页崩溃，改用VP8推流后会频现看不到坐席画面的问题，暂作不兼容处理
        const crashIosInterval = [
          compareVersionsCompare(getIOSVersion(), '15.0.0', '>='),
          compareVersionsCompare(getIOSVersion(), '15.3', '<=')
        ];

        const crashIosInterval2 = [
          compareVersionsCompare(getIOSVersion(), '16.0', '>='),
          compareVersionsCompare(getIOSVersion(), '16.1', '<=')
        ];

        const specialAndroid = [
          // 小米note
          /mi note( lte)? build/.test(navigator.userAgent.toLowerCase()),
          isXiaomiWechat()
        ];

        if (
          (ISIOS && specialIosInterval.every((item) => item)) ||
          (ISIOS && crashIosInterval.every((item) => item)) ||
          (ISIOS && crashIosInterval2.every((item) => item)) ||
          (ISANDROID && specialAndroid.some((item) => item))
        ) {
          specialFlag = false;
        } else {
          specialFlag = true;
        }
        isSupportWebRTC((res) => {
          if (res.error_no !== 0 || !specialFlag) {
            this.$TAlert({
              title: '温馨提示',
              tips: '当前浏览器不支持视频见证，请更换浏览器或联系客服电话95310'
            });
          } else {
            if (!this.witnessPeriod) {
              this.$TAlert({
                title: '温馨提示',
                tips: '当前非见证办理时间'
              });
              return;
            }
            let tips =
              '根据中国证券监督管理委员会、中国证券登记结算有限责任公司、沪深交易所的相关要求，在您本次办理业务时，需向我们提供个人生物识别信息，包括个人视频、个人照片。本次信息采集仅用于业务办理，且仅向相关机构报送必要信息。请确认您已知晓并同意提供以上信息：';
            this.$TAlert({
              title: '温馨提示',
              tips: tips,
              hasCancel: true,
              confirmBtn: '同意',
              cancelBtn: '不同意',
              confirm: () => {
                addClientCritMark({
                  flowToken: sessionStorage.getItem('TKFlowToken'),
                  markType: '12',
                  markContent: tips,
                  confirmFlag: '1'
                }).then((res) => {
                  this.toRegist();
                });
              }
            });
          }
        });
      } else {
        if (!this.witnessPeriod) {
          this.$TAlert({
            title: '温馨提示',
            tips: '当前非见证办理时间'
          });
          return;
        }
        let tips =
          '根据中国证券监督管理委员会、中国证券登记结算有限责任公司、沪深交易所的相关要求，在您本次办理业务时，需向我们提供个人生物识别信息，包括个人视频、个人照片。本次信息采集仅用于业务办理，且仅向相关机构报送必要信息。请确认您已知晓并同意提供以上信息：';
        this.$TAlert({
          title: '温馨提示',
          tips: tips,
          hasCancel: true,
          confirmBtn: '同意',
          cancelBtn: '不同意',
          confirm: () => {
            addClientCritMark({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              markType: '12',
              markContent: tips,
              confirmFlag: '1'
            }).then((res) => {
              this.toRegist();
            });
          }
        });
      }
    },

    toRegist() {
      this.eventMessage(this, 'closeReject');
      let param = {
        flowToken: sessionStorage.getItem('TKFlowToken'),
        custId: $h.getSession('authorization'),
        devicePlatform: this.devicePlatform,
        deviceSysVersion: this.deviceSysVersion,
        operationType: '1' // 操作类别，1、见证；2、审核
      };
      return videoRegist(param)
        .then((data) => {
          if (data.code == '0') {
            if (!data.data) {
              _hvueToast({
                mes: '注册失败'
              });
              return;
            }
            this.regResult = Object.assign(this, data.data);
            this.regResult = Object.assign(
              this.regResult,
              data.data.clientInfo
            );
            $h.setSession('videoToken', this.regResult.jwtToken);
            // this.startTwoVideo();
            this.childUrl = window.location.origin + '/auth-living-view/views/index.html';

            // this.childUrl =
            //   'http://localhost:8080/auth-living-view/views/index.html';

            if (this.isApp) {
              this.startTwoVideo();
            } else {
              this.startH5 = true;
            }
          } else {
            _hvueToast({
              mes: data.msg
            });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },

    videoCallBack(msg = {}) {
      this.pageState = 1;
      console.log(msg);
      let message = JSON.parse(msg.message || '');
      if (message.msgNo == 0 || message.msgNo == 1) {
        this.getVideoResult();
      }
    },

    nextClick() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    },

    async rejectNext() {
      if (!this.notNext) {
        this.nextClick();
      } else {
        if (this.isApp) {
          await this.toRegist();
          this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: true });
          this.pageState = 1;
        } else {
          await this.toRegist();
          this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: true });
          this.H5callback = false;
        }
      }
    },

    rejectHandler(msgArr, notNext) {
      this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: false });
      this.pageState = 4;
      this.notNext = notNext;
      this.rejectReason = msgArr;
    },

    async queryVideoResult(regFlowNo) {
      const flowData = await flowQueryIns({
        flowToken: sessionStorage.getItem('TKFlowToken')
      });
      if (flowData.code == 0) {
        const { status = '' } = flowData.data;
        // 当前流程状态为受理中，则查询视频结果
        if (status === PROCESS_STATUS.ACCEPTING) {
          const { data, code } = await getVideoResult({
            regFlowNo,
            flowToken: sessionStorage.getItem('TKFlowToken'),
            source: $hvue.platform === '0' ? '3' : $hvue.platform
          });
          if (code == 0) {
            //opFlowStatus 1待处理 2通过 3驳回
            if (data.opFlowStatus == 2) {
              return this.nextClick();
            }
          }
        }
      }
    },

    getVideoResult() {
      this.startH5 = false;
      getVideoResult({
        regFlowNo: this.regResult.regFlowNo,
        flowToken: sessionStorage.getItem('TKFlowToken'),
        source: $hvue.platform === '0' ? '3' : $hvue.platform
      }).then((data) => {
        if (data.code == 0) {
          let result = data.data;
          this.eventMessage(this, EVENT_NAME.BACK_BTN, { display: true });
          if (result.opFlowStatus == 2) {
            // 1待处理 2通过 3驳回
            // this.pageState = 3;
            this.nextClick();
          } else if (result.opFlowStatus == 3) {
            this.rejectHandler(result.witnessMsg);
          }
        } else {
          if (data.code == 2002) {
            //仅仅驳回了视频，算组件内错误，不需要调用流程引擎下一步接口
            this.rejectHandler(data.msg.split(';'), true);
          } else if (data.code == 3001) {
            const that = this;
            import('@/common/flowMixinV2.js').then((a) => {
              a.mountRejectResultComponent(data.msg, {
                title: '见证不通过',
                rejectConfirm: () => {
                  a.initFlow.call(that, { bizType: that.regResult.bizType, initJumpMode: '1', cancelFlage: false });
                },
                back: () => that.eventMessage(this, EVENT_NAME.TO_INDEX),
                toIndex: () => that.eventMessage(this, EVENT_NAME.TO_INDEX)
              });
            });
          } else {
            _hvueAlert({ mes: data.msg });
          }
        }
      });
    }
  }
};
</script>

<style></style>
