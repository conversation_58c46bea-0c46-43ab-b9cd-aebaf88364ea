<template>
  <div class="agree_fixed" style="z-index: 1400">
    <t-header @back="back" />
    <article
      ref="protocolCont"
      class="content"
      style="background: #ffffff; -webkit-overflow-scrolling: touch"
    >
      <div
        v-if="agreementHtml"
        class="protocol_cont"
        ref="protocalContent"
      ></div>
      <template v-else-if="pdfSrc">
        <div class="file_scrollbox" style="width: 100%; height: 100%">
          <tk-pdf :src="pdfSrc" />
        </div>
      </template>
    </article>
    <footer class="footer" style="background: #ffffff">
      <div class="ce_btn">
        <a
          class="p_button"
          :class="{ disabled: !loadingEd }"
          @click="submitForm"
        >
          <span>我已理解并同意</span>
        </a>
      </div>
    </footer>
  </div>
</template>

<script>
import { queryAgreementExtV3 } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { signAgreeV2 } from '@/common/util';
import { getProtocolDetail } from '@/common/utils/protocols.util';
import tkPdf from '@/plugins/vue-pdf/tkPdf.vue';

export default {
  name: 'CancelTips',
  inject: ['tkFlowInfo', 'clearKeepAlive', 'eventMessage'],
  components: { tkPdf },
  data() {
    return {
      onlyShowOne: false,
      loadingEd: false,
      showAgreeDetail: false,
      isChecked: false,
      agreeIndex: 0,
      readList: [],
      agreeList: [],
      agreementHtml: null,
      pdfSrc: null
    };
  },
  computed: {},
  created() {
    this._queryAgreement();
    this.clearKeepAlive();
  },
  methods: {
    getProtocolParam() {
      let param = {
        agreementBizType: this.$attrs.agreementNodeNo.split(':')[0],
        agreementNodeNo: this.$attrs.agreementNodeNo.split(':')[1],
        agreementExt: this.$attrs.agreementExt
      };
      try {
        return param;
      } catch (e) {
        console.log(e);
        return param;
      }
    },

    async _queryAgreement() {
      try {
        let param = this.getProtocolParam();
        let reqParams = {
          flowToken: sessionStorage.getItem('TKFlowToken'),
          ...param
        };
        const res = await queryAgreementExtV3(reqParams);
        const { agreementList, epaperESignedFlag } = res.data;
        this.agreeList = agreementList.map((it) => {
          it.epaperESignedFlag = epaperESignedFlag;
          it.agreementBizType = param.agreementBizType;
          return it;
        });
        if (agreementList.length === 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP);
          return;
        }
        const protocolInfo = await getProtocolDetail({ ...agreementList[0] });
        if (protocolInfo.agreementPDF) {
          this.pdfSrc = protocolInfo.agreementPDF;
        } else if (protocolInfo.agreementHtml) {
          this.$nextTick(() => {
            this.agreementHtml = protocolInfo.agreementHtml;
            this.$nextTick(() => {
              this.$refs.protocalContent.innerHTML = protocolInfo.agreementHtml;
            });
          });
        }
        this.loadingEd = true;
      } catch (error) {
        _hvueToast({
          mes: error
        });
        console.error(error);
      }
    },
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    submitForm() {
      if (!this.loadingEd) return;
      const tkFlowInfo = this.tkFlowInfo();
      signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            epaperSignJson
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    }
  }
};
</script>
<style scoped>
.protocol_cont {
  overflow: auto;
  padding: 0.15rem;
  font-size: 0.16rem;
  line-height: 0.26rem;
}
.protocol_cont p {
  padding: 0.05rem 0;
}
.protocol_cont h1 {
  font-size: 0.18rem;
}
.protocol_pdf {
  width: 100%;
}
div.agree_fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
}
div.agree_fixed article.content {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: auto;
  position: relative;
}
div.agree_fixed article.content::-webkit-scrollbar {
  width: 0;
  display: none;
}
/* table 样式 */
table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
table td,
table th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
}
table th {
  border-bottom: 2px solid #ccc;
  text-align: center;
}

/* blockquote 样式 */
blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}

/* code 样式 */
code {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}
pre code {
  display: block;
}

/* ul ol 样式 */
ul,
ol {
  margin: 10px 0 10px 20px;
}

.protocol_cont >>> table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.protocol_cont >>> table > tbody > tr > td {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
}
.protocol_cont >>> table > tbody > tr > td > p {
  word-wrap: break-word;
  word-break: break-all;
}
.protocol_cont >>> b {
  font-weight: bold;
}
.protocol_cont >>> em {
  font-style: italic;
}
</style>
