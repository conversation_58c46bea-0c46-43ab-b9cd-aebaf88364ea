/*-- public set  --*/
@bg-color:#F2F4F8;
@border-color:rgba(221, 221, 221, 0.5);
@main-color: #F93838;
@t-color-normal: #0F1826;
@t-color-gray: #55555E;
@t-color-lightgray: #87878D;
@link-color: #338AFF;
@error-color: #FF7015;
@tips-color: #FF8533;
@imp_color: #FF8533;
@button-bg-1: #F93838;
@button-bg-2: #F93838;
@button-border-bg: #FFF0F0;
@button-assist-bg: #FFF0F0;
@disbaled-bg: #CCCCCC;
@disbaled-bg-50: rgba(221, 221, 221, 0.5);
@text-tip-color: #CCCCCC;

@border-deep-color:#BBBBBB;
@suc-color: #FF2840;
@suc-assist-color: #FF2840;
@border-light-color: #E9E9EB;
@main-color-3: rgba(255, 40, 64, 0.03);
@main-color-10: rgba(255, 40, 64, 0.1);
@main-color-30: rgba(255, 40, 64, 0.3);
@main-color-50: rgba(255, 40, 64, 0.5);
@link-color-10: rgba(51, 138, 255, 0.1);
@normal-assist-color: #B36E00;

@suc-color-2: #FF2840;
@error-color-2: #FF7015;

@button-radius: 0.5rem;
@module-radius: 0.02rem;
@module-radius-2: 0.08rem;

@font-form-size: 0.16rem;
@font-base-size: 0.14rem;
@font-tip-size: 0.12rem;
@font-title-size: 0.18rem;
@font-sub-title-size: 0.16rem;
@font-head-title-size: 0.16rem;
@font-spel-size-1: 0.2rem;
@font-spel-size-2: 0.24rem;





body {
  position: relative;
  overflow-y: visible !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: moz-none;
  user-select: none;
  -webkit-overflow-scrolling: auto;
}
body * {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
}

/** html4 reset **/
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td {
  margin: 0;
  padding: 0;
}
fieldset, img {
  border: 0 none;
}
address, caption, cite, code, dfn, em, th, var, b,h1,h2,h3 {
  font-style: normal;
  font-weight: normal;
}
p {
  margin: 0;
}
ol, ul, li {
  list-style-type: none
}
q:before, q:after {
  content: '';
}
abbr, acronym {
  border: 0;
  font-variant: normal;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
th,td,caption {
  vertical-align: top;
  text-align: left;
}
input[type="text"],
input[type="email"],
input[type="search"],
input[type="password"],
input[type="date"],
input[type="month"],
input[type="tel"],
button, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
input[type="search"] {
  -webkit-appearance: textfield;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
img {
  vertical-align: middle;
  font-size: 0;
}

/** html5 reset **/
header, footer, section, nav, menu, details, hgroup, figure, figcaption, article, aside {
  margin: 0;
  padding: 0;
  display: block;
}
input::-moz-placeholder {
  color: @text-tip-color;
}

input::-webkit-input-placeholder {
  color: @text-tip-color;
}

textarea::-moz-placeholder {
  color: @text-tip-color;
}

textarea::-webkit-input-placeholder {
  color: @text-tip-color;
}

input,
textarea {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
a {
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}
a:hover {
  opacity: 1
}

.clear {
  clear: both;
  font-size: 0;
  height: 0;
  line-height: 0;
  overflow: hidden;
}
.clearfix:after {
  clear: both;
  content: "";
  display: block;
  font-size: 0;
  height: 0;
  visibility: hidden;
}
.clearfix {
  zoom:1;}

/** Body, links, basics **/
body,html {
  font-size: 100px;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
body {
  font-size: @font-base-size;
  line-height: 1.42;
  background: @bg-color;
  font-family:-apple-system-font, "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-weight: normal;
  color: @t-color-normal;
  word-break: break-all;
}


/*-- 布局grid --*/

.main.fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;

  article.content {
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    height: 100%;
    -webkit-overflow-scrolling: auto;
    position: relative;

    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }
  }
}


/*-- 表单 form --*/

.input_form {
  background: #ffffff;
  padding: 0 0.16rem;
}

.input_text {
  border-bottom: 1px solid @border-color;
  position: relative;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  min-height: 0.56rem;

  &:last-child {
    border-bottom: 0 none;
  }
  .tit {
    font-size: @font-form-size;
    line-height: 1.5;
    padding: 0.16rem 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;

    .imp{
      color: @tips-color;
      margin-left: 0.04rem;
    }
  }
  .t1 {
    display: block;
    width: 100%;
    height: 0.56rem;
    padding: 0.16rem 0;
    line-height: 1.5;
    font-size: @font-form-size;
    border: 0 none;
    outline: none;
    background: none;
    font-family:-apple-system-font, "Helvetica Neue", sans-serif;
    color: @t-color-normal;
    &.disabled,
    &[disabled="disabled"]{
      color: @t-color-lightgray;
      -webkit-text-fill-color: @t-color-lightgray;
    }

  }
  .t1[type="password"]{
    letter-spacing: 0.06rem;
  }
  .t1[type="password"]::-moz-placeholder {
    letter-spacing: 0;
  }
  .t1[type="password"]::-webkit-input-placeholder {
    letter-spacing: 0;
  }
  .dropdown {
    min-height: 0.56rem;
    line-height: 1.5;
    padding: 0.16rem 0.24rem 0.16rem 0;
    font-size: @font-form-size;
    color:@t-color-normal;
    position: relative;

    &:after {
      content: "\e619";
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: @border-deep-color;
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 0.16rem;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: 0;
    }
    &:empty:before {
      content: attr(placeholder);
      color: @text-tip-color;
    }

    img {
      width: 0.24rem;
      height: 0.24rem;
      float: left;
      margin-right: 0.1rem;
    }
    &.disabled,
    &[disabled="disabled"]{
      color: @t-color-lightgray;
      -webkit-text-fill-color: @t-color-lightgray;
    }
  }
  .tarea1 {
    padding: 0.16rem 0;
    line-height: 1.5;
    min-height: 0.56rem;
    width: 100%;
    font-family:-apple-system-font, "Helvetica Neue", sans-serif;
    color: @t-color-normal;
    font-size: @font-form-size;
    outline: none;
    border: 0 none;
    background: none;
    -webkit-user-select: auto;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    display: block;

    &:empty:before {
      content: attr(placeholder);
      color: @text-tip-color;
    }
    &.disabled,
    &[disabled="disabled"]{
      color: @t-color-lightgray;
      -webkit-text-fill-color: @t-color-lightgray;
    }
  }
  .input_ct{
    padding: 0.16rem 0;
    font-size: @font-form-size;
    line-height: 1.5;
    min-height: 0.56rem;
    position: relative;
  }
  &.text{

    .t1,
    .dropdown,
    .tarea1 {
      padding-left: 0.88rem;
    }
    .error_tips{
      padding-left: 0.88rem;
    }
    .input_ct{
      padding-left: 0.88rem;
    }
  }
}
.error_tips {
  font-size: @font-base-size;
  line-height: 1.4;
  color: @error-color;
  position: relative;
  margin-top: 0.04rem;
  top: -0.16rem;
}

.code_img {
  width: 0.8rem;
  height: 0.28rem;
  position: absolute;
  top: 50%;
  margin-top: -0.14rem;
  right: 0;
  z-index: 50;

  img {
    display: block;
    width: 100%;
    height: 100%;
  }
}

.code_btn,
.code_btn2{
  line-height: 1.5;
  background: #ffffff;
  text-align: center;
  font-size: @font-form-size;
  color: @link-color;
  position: absolute;
  top: 50%;
  margin-top: -0.12rem;
  right: 0;
  z-index: 50;

  &.time {
    color: @text-tip-color;
  }
  &.disabled {
    color: @t-color-lightgray !important;
  }
}
.txt_close {
  width: 0.24rem;
  height: 0.24rem;
  text-align: center;
  line-height: 0.24rem;
  font-size: 0.16rem;
  color: @t-color-lightgray;
  position: absolute;
  top: 50%;
  margin-top: -0.12rem;
  right: 0;
  z-index: 50;

  &:before{
    content: "\e623";
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

.input_text.code .txt_close {
  right: 1rem;
}

.input_text.pword .txt_close {
  right: 0.3rem;
}


/*-- 单选、复选框 radio checkbox --*/
.icon_check {
  display: inline-block;
  padding-left: 0.24rem;
  line-height: 1.5;
  position: relative;

  &:before {
    content: "";
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 0.18rem;
    height: 0.18rem;
    border: 1px solid @border-deep-color;
    border-radius: 0.04rem;
    font-size: 0.16rem;
    line-height: 1;
    color: #ffffff;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    left: 0;
    margin: -0.09rem 0 0 0;
  }
  &.checked:before {
    content: "\e61f";
    border-color: @main-color;
    background: @main-color;
  }
  &.disabled:before {
    border-color: @disbaled-bg;
    background-color: @disbaled-bg-50;
  }
  &.checked.disabled:before{
    border-color: @disbaled-bg;
    background-color: @disbaled-bg-50;
  }
}
.icon_radio {
  display: inline-block;
  padding-left: 0.24rem;
  line-height: 1.5;
  position: relative;

  &:before {
    content: '';
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 0.18rem;
    height: 0.18rem;
    border: 1px solid @border-deep-color;
    border-radius: 50%;
    font-size: 0.16rem;
    line-height: 1;
    color: #ffffff;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    left: 0;
    margin: -0.09rem 0 0 0;
  }

  &.checked:before {
    content: "\e61f";
    border-color: @main-color;
    background: @main-color;
  }
  &.disabled:before {
    border-color: @disbaled-bg;
    background-color: @disbaled-bg-50;
  }
  &.checked.disabled:before{
    border-color: @disbaled-bg;
    background-color: @disbaled-bg-50;
  }
}

/*-- 开关 switch --*/

.switch {
  display: inline-block;
  vertical-align: top;
  height: 0.24rem;
  width: 0.45rem;
  position: relative;

  >input[type='checkbox'] {
    width: 0.45rem;
    height: 0.24rem;
    position: absolute;
    left: 0;
    top: 0;
    -moz-opacity: 0;
    -webkit-opacity: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 100;
    outline: none;
  }
  >.switch-inner {
    height: 0.24rem;
    position: relative;
    background: @border-light-color;
    -moz-border-radius: 1rem;
    -webkit-border-radius: 1rem;
    border-radius: 1rem;
    transition: all 0.1s ease-in;
    -moz-transition: all 0.1s ease-in;
    -webkit-transition: all 0.1s ease-in;

    >.switch-arrow {
      height: 0.16rem;
      width: 0.16rem;
      background: #ffffff;
      position: absolute;
      top: 0.04rem;
      right: 0.25rem;
      -moz-border-radius: 1rem;
      -webkit-border-radius: 1rem;
      border-radius: 1rem;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.1s ease-in;
      -moz-transition: all 0.1s ease-in;
      -webkit-transition: all 0.1s ease-in;
      z-index: 10;
    }

  }
}

.switch>input[type='checkbox']:checked+.switch-inner {
  background: @main-color;
}

.switch>input[type='checkbox']:checked+.switch-inner .switch-arrow {
  right: 0.04rem;
}

/*-- 头部header --*/
.header {
  background: #ffffff;
  position: relative;
  z-index: 200;

}

.header_inner {
  position: relative;
  height: 0.44rem;
  line-height: 0.44rem;

  >h1.title {
    font-size: @font-head-title-size;
    font-weight: 500;
    color: #000000;
    position: relative;
    z-index: 0;
    text-align: center;
    margin: 0 0.5rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}


.icon_text {
  position: absolute;
  top: 0;
  right: 0;
  font-size: @font-base-size;
  padding: 0 0.16rem;
  color: #000000;
  z-index: 50;
}

.icon_back {
  width: 0.48rem;
  height: 0.44rem;
  line-height: 0.44rem;
  text-align: center;
  font-size: 0.24rem;
  color: @t-color-normal;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &:before{
    content: "\e61a";
  }
}
.icon_close {
  width: 0.48rem;
  height: 0.44rem;
  line-height: 0.44rem;
  text-align: center;
  font-size: 0.22rem;
  color: @t-color-normal;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 50;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &:before{
    content: "\e61d";
  }
}
.icon_home{
  width: 0.48rem;
  height: 0.44rem;
  line-height: 0.44rem;
  text-align: center;
  font-size: 0.2rem;
  color: @t-color-normal;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 50;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &:before{
    content: "\e62c";
  }
}

/*-- 其他公用 public  --*/

.mt10 {
  margin-top: 0.08rem !important;
}

.mt15 {
  margin-top: 0.15rem !important;
}

.mt20 {
  margin-top: 0.2rem !important;
}

.mt30 {
  margin-top: 0.3rem !important;
}

.mt50{
  margin-top: 0.5rem !important;
}

.com_title {
  padding: 0.15rem 0.16rem;
  background: @bg-color;
  line-height: 1.4;
  font-size: @font-base-size;
  font-weight: 500;
  display: flex;

  &.white_bg{
    color: @t-color-lightgray;
  }

  h5{
    font-weight: 500;
    font-size: @font-base-size;
    flex: 1;
    width: 100%;
  }
}

.dialog_overlay {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5000;
}

.dialog_box {
  width: 3rem;
  background: #fff;
  border-radius: @module-radius-2;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -1.5rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 6000;
}

.dialog_cont {
  padding: 0.24rem;

  h3 {
    font-size: @font-title-size;
    line-height: 1.5;
    text-align: center;
    font-weight: 500;
    margin-bottom: 0.08rem;
  }
  >div {
    overflow: auto;
    max-height: 3.6rem;
    text-align: center;
    font-size: 0.15rem;
    line-height: 1.5;
    color: @t-color-normal;
  }

}
.dialog_btn {
  border-top: 1px solid @border-color;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: center;

  a {
    display: block;
    flex: 1;
    border-left: 1px solid @border-color;
    line-height: 1.5;
    padding: 0.16rem 0;
    font-size: @font-sub-title-size;
    text-align: center;
    color: @button-bg-1;

    &:first-child {
      border-left: 0 none;
    }
    &.cancel {
      color: @t-color-gray;
    }
    &.disabled {
      color: @t-color-lightgray !important;
    }
  }
}
.dialog_tip_icon{
  min-height: 0 !important;
  height: 0 !important;
}

.toast_popup {
  width: 2.2rem;
  text-align: center;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -1.1rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1000;

  p {
    display: inline-block;
    padding: 0.14rem 0.22rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: @module-radius-2;
    color: #ffffff;
    font-size: @font-base-size;
    line-height: 1.5;
    min-width: 1.4rem;
  }
}

.ared{
  color: @error-color !important;
}

.com_link {
  color: @link-color;
}

.txt_center {
  text-align: center !important;
}

.txt_left {
  text-align: left !important;
}

.txt_right {
  text-align: right !important;
}
.p_button {
  display: block;
  height: 0.44rem;
  line-height: 2.75;
  text-align: center;
  font-size: @font-form-size;
  background: linear-gradient(90deg, @button-bg-1 0%, @button-bg-2 100%);
  color: #ffffff;
  border-radius: @button-radius;
  font-weight: 500;

  &.border {
    background: @button-border-bg;
    color: @button-bg-1;
  }
  &.disabled {
    background: @disbaled-bg !important;
    color: #ffffff !important;
  }
}
.ce_btn {
  display: flex;
  padding: 0.15rem 0.16rem;

  a {
    flex: 1;
    margin-left: 0.12rem;

    &:first-child {
      margin-left: 0;
    }
  }
  &.block{
    display: block;

    a{
      margin-left: 0;
      margin-top: 0.12rem;

      &:first-child{
        margin-top: 0;
      }
    }
  }
}
.footer .ce_btn {
  padding: 0.06rem 0.16rem;
}
.footer .rule_check + .ce_btn{
  padding-top: 0;
}


.white_bg,
.h5_white_bg{
  background: #ffffff !important;
}

.com_box{
  background: #ffffff;
}

.com_span{
  color: @main-color !important;
}

.top_border {
  border-top: 1px solid @border-color;
}


/*-- 业务入口 bus_enter  --*/
.bus_tp_module{
  padding: 0.12rem 0 0;
  background: #ffffff;

  &.hot{
    padding: 0 0 0.12rem;
    margin-bottom: 0.08rem;
    border-radius: 0 0 0.16rem 0.16rem;
  }

  &:last-child{
    padding-bottom: 0.12rem;
  }
}

.bus_tp_title{
  line-height: 0.28rem;
  font-size: @font-sub-title-size;
  padding: 0.1rem 0.2rem;
  font-weight: 500;


  img{
    width: 0.18rem;
    height: 0.18rem;
    position: relative;
    top: 0.05rem;
    margin-right: 0.08rem;
    display: inline-block;
    vertical-align: top;
  }
}
.bus_hot_list{
  overflow: hidden;
  display: flex;
  padding: 0 0.1rem;

  li{
    padding: 0.1rem 0 0.08rem;
    flex: 1;
    min-width: 25%;

    a{
      display: block;
      font-size: @font-base-size;
      line-height: 1.25;
      color: @t-color-normal;
      text-align: center;
      padding: 0 0.08rem;

      img{
        display: block;
        width: 0.36rem;
        height: 0.36rem;
        margin: 0 auto 0.07rem;
      }

      span{
        display: block;
      }
    }
  }
}
.bus_navlist {
  overflow: hidden;

  li{
    width: 50%;
    float: left;
  }

  a{
    display: block;
    font-size: 0.15rem;
    line-height: 1.5;
    padding: 0.12rem 0 0.12rem 0.34rem;
    color: @t-color-normal;
    position: relative;

    &:before{
      content: "";
      width: 0.04rem;
      height: 0.04rem;
      background: @main-color-30;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      margin-top: -0.02rem;
      left: 0.2rem;
    }
    span{
      position: relative;
    }
  }
}
.bus_ic_new{
  display: inline-block;
  top: 0.01rem;
  width: 0.2rem;
  height: 0.2rem;
  text-align: center;
  font-style: normal;
  line-height: 0.2rem;
  background: @main-color;
  font-size: 0.12rem;
  color: #ffffff;
  border-radius: 0.05rem 0.05rem 0.05rem 0;
  -webkit-transform: scale(0.75);
  transform: scale(0.75);
  position: absolute;
  bottom: 0.01rem;
  right: -0.22rem;
}

/*-- 首页 home  --*/
.header.fixed_header{
  z-index: 200;
  width: 100%;
  background: none;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transition: background-color 0.5s;
  transition: background-color 0.5s;

  .header_inner > h1.title,
  .icon_text{
    color: #fff;
    -webkit-transition: color 0.5s;
    transition: color 0.5s;
  }

  .icon_back{
    color: #ffffff;
  }
  &.active{
    background-color: #ffffff;

    .header_inner > h1.title,
    .icon_text{
      color: #000000;
    }

    .icon_back{
      color: @t-color-normal
    }
  }
}

.h5_home_page{
  background: #ffffff;
  min-height: 100%;
}
.h5_banner_box{
  margin-bottom: -28%;
  min-height: 2.6rem;

  img{
    display: block;
    width: 100%;
  }
}
.bus_home_must{
  margin: 0 0.2rem 0.2rem;
  padding: 0.2rem;
  border-radius: @module-radius;
  background: #FEFFFE;
  box-shadow: 0 0.02rem 0.1rem 0 rgba(164, 108, 70, 0.12);
  position: relative;
  z-index: 50;

  &:before{
    content: "";
    width: 100%;
    border-radius: @module-radius @module-radius 0 0;
    height: 0.4rem;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, #D5E3FF 0%, #FEFFFE 100%);
  }
  .title{
    text-align: center;
    font-size: @font-spel-size-1;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 0.18rem;
    position: relative;
    z-index: 50;
    color: #000000;

    span{
      position: relative;
      display: inline-block;
      vertical-align: top;

      &:before{
        content: "";
        width: 100%;
        height: 0.1rem;
        background: rgba(16, 97, 255, 0.1);
        border-radius: @module-radius;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      em{
        position: relative;
        z-index: 50;
        font-weight: 500;
      }
    }
  }
  .list{
    position: relative;
    z-index: 50;

    li{
      padding: 0.04rem 0 0.04rem 0.18rem;
      font-size: @font-base-size;
      line-height: 1.5;
      position: relative;
      color: @t-color-gray;

      &:before{
        content: "";
        width: 0.08rem;
        height: 0.08rem;
        background: url(../images/dot_star.png) no-repeat center;
        background-size: 100%;
        position: absolute;
        top: 0.1rem;
        left: 0;
      }
    }
  }
}
.bus_home_tips{
  margin: 0.2rem;

  h5{
    font-size: @font-sub-title-size;
    line-height: 1.5;
    font-weight: 500;
    margin-bottom: 0.05rem;
  }
  p{
    font-size: 0.12rem;
    line-height: 0.2rem;
    color: @t-color-lightgray;
  }
}

/*-- 前置条件检查  --*/
.cond_list{
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    padding: 0.12rem 0;

    .tit{
      position: relative;
      min-height: 0.32rem;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      h5{
        font-size: @font-base-size;
        line-height: 1.42;
        padding-left: 0.32rem;
        position: relative;
        font-weight: normal;
        width: 100%;

        &:before{
          content: "";
          display: block;
          width: 0.2rem;
          height: 0.2rem;
          font-size: 0.2rem;
          line-height: 1;
          text-align: center;
          font-family: "wt-iconfont" !important;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      p{
        margin-left: 0.32rem;
        font-size: @font-base-size;
        line-height: 1.5;
        color: @t-color-lightgray;
        margin-top: 0.08rem;
        width: 100%;
      }
    }
    &.ok{
      .tit h5:before{
        content: "\e621";
        color: @suc-color;
      }
    }
    &.error{
      .tit h5:before{
        content: "\e61e";
        color: @error-color;
      }
    }
    .p_right{
      padding-right: 0.7rem;
    }
    .btn{
      font-size: @font-base-size;
      line-height: 0.28rem;
      background: none;
      padding: 0 0.12rem;
      position: absolute;
      top: 0;
      right: 0;
      color: @button-bg-1;
      background: @button-assist-bg;
      border-radius: 0.5rem;
      z-index: 50;
    }
  }
}
.cond_info_layout{
  display: flex;
  width: 100%;
  align-items: center;
  line-height: 0.32rem;

  .num{
    display: block;
    font-size: 0.22rem;
    flex: 1;
    width: 100%;

    &.ared{
      color: @error-color !important;
    }
  }
  .btn{
    position: relative !important;
    margin-left: 0.16rem;
  }
}
.cond_tips{
  text-align: center;
  margin: 0.2rem 0.45rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;
}
.warm_tips{
  padding: 0 0.16rem;
  color: #55555e;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0px;
  text-align: left;
}


/*-- 上传身份证  --*/
.upload_com_box{
  background: #ffffff;
  border-radius: 0;
}
.upload_wrap{
  background: #ffffff;
  padding: 0.2rem 0.15rem;
  display: flex;
}
.upload_item{
  width: 100%;
  flex: 1;
  position: relative;
  margin-left: 0.15rem;

  &:first-child{
    margin-left: 0;
  }
  .pic{
    overflow: hidden;
    position: relative;
    background: #E8F0FF;
    padding-top: 62.4%;
    border-radius: @module-radius;

    img{
      display: block;
      width: 100%;
      position: absolute;
      top: 50%;
      left: 0;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
  .btn{
    width: 100%;
    height: 100%;
    padding-top: 39%;
    text-align: center;
    font-size: @font-base-size;
    color: @button-bg-1;
    line-height: 1.42;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;

    &:before{
      content: "";
      width: 0.32rem;
      height: 0.28rem;
      background: url(../images/icon_photo01.png) no-repeat center;
      background-size: 100%;
      position: absolute;
      top: 0;
      left: 50%;
      margin-top: 15%;
      margin-left: -0.16rem;
    }
  }
}
.upload_item_assist{
  width: 100%;
  flex: 1;
  position: relative;
  margin-left: 0.15rem;

  &:first-child{
    margin-left: 0;
  }

  .pic{
    overflow: hidden;
    position: relative;
    background: #FFF0F0;
    padding-top: 62.5%;
    border-radius: @module-radius;

    img{
      display: block;
      width: 100%;
      position: absolute;
      top: 50%;
      left: 0;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
  .btn{
    width: 100%;
    height: 100%;
    padding-top: 39%;
    text-align: center;
    font-size: @font-base-size;
    color: @button-bg-1;
    line-height: 1.42;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;

    &:before{
      content: "";
      width: 0.3rem;
      height: 0.28rem;
      background: url(../images/icon_photo01.png) no-repeat center;
      background-size: 100%;
      position: absolute;
      top: 0;
      left: 50%;
      margin-top: 15%;
      margin-left: -0.15rem;
    }
  }
  .reset_btn {
    padding: 0 0.11rem 0 0.32rem;
    line-height: 2.25;
    font-size: @font-tip-size;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    border-radius: @button-radius;
    position: absolute;
    bottom: 0.14rem;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    z-index: 100;

    &:before {
      content: '';
      width: 0.16rem;
      height: 0.16rem;
      background: url(../images/icon_photo02.png) no-repeat center;
      background-size: 100%;
      position: absolute;
      left: 0.11rem;
      top: 50%;
      margin-top: -0.08rem;
    }
  }

}
.upload_link{
  padding: 62.5% 0 0;
  position: relative;

  a{
    display: inline-block;
    background: @button-border-bg;
    color: @button-bg-1;
    width: auto;
    padding: 0 0.12rem;
    text-align: center;
    line-height: 2;
    border-radius: @button-radius;
    font-size: @font-base-size;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    z-index: 50;
  }
}
.photo_tips{
  padding: 0.16rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-normal;

  .title{
    font-size: @font-base-size;
    line-height: 1.42;
    padding: 0.03rem 0;
    font-weight: 500;
    margin-bottom: 0.15rem;
    color: #000000;
  }
  p{
    font-size: @font-base-size;
    line-height: 1.42;
    padding: 0.02rem 0;

    .imp{
      color: @tips-color;
    }
  }
  .list + p{
    margin-top: 0.2rem;
  }
  .list{
    display: flex;
    margin: 0.15rem 0 0.2rem;

    li{
      flex: 1;
      margin-left: 0.12rem;
      min-width: 0;

      &:first-child{
        margin-left: 0;
      }
      .pic{
        margin-bottom: 0.08rem;

        img{
          display: block;
          width: 100%;
          border-radius: @module-radius;
        }
      }
      span{
        display: block;
        font-size: @font-base-size;
        line-height: 1.42;
        padding-left: 0.16rem;
        position: relative;
        white-space: nowrap;

        &:before{
          content: "";
          width: 0.12rem;
          height: 0.12rem;
          text-align: center;
          font-size: 0.12rem;
          line-height: 1;
          font-family: "wt-iconfont" !important;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          position: absolute;
          top: 50%;
          margin-top: -0.06rem;
          left: 0;
        }
        &.ok:before{
          content: "\e651";
          color: @suc-color;
        }
        &.error:before{
          content: "\e652";
          color: @error-color;
        }
      }
    }
  }
}

.upload_select {
  width: 100%;
  padding: 0.16rem;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 5000;

  h5 {
    background: #fff;
    border-radius: 0.12rem 0.12rem 0 0;
    font-size: @font-base-size;
    font-weight: normal;
    color: @t-color-lightgray;
    text-align: center;
    line-height: 3;
    border-bottom: 1px solid @border-color;
  }

  ul {
    background: #fff;
    border-radius: 0 0 0.12rem 0.12rem;

    li {
      border-bottom: 1px solid @border-color;

      &:last-child {
        border-bottom: 0 none;
      }
      a {
        display: block;
        text-align: center;
        line-height: 3;
        font-size: @font-title-size;
        color: @button-bg-1;
      }
    }
  }
  .cancel {
    margin-top: 0.06rem;
    display: block;
    line-height: 3;
    text-align: center;
    font-size: @font-title-size;
    color: @button-bg-1;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.97);
    border-radius: 0.12rem;
  }
}

.reset_btn {
  padding: 0 0.11rem 0 0.32rem;
  line-height: 2.25;
  font-size: @font-tip-size;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  border-radius: @button-radius;
  position: absolute;
  bottom: 0.14rem;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: 100;

  &:before {
    content: '';
    width: 0.16rem;
    height: 0.16rem;
    background: url(../images/icon_photo02.png) no-repeat center;
    background-size: 100%;
    position: absolute;
    left: 0.11rem;
    top: 50%;
    margin-top: -0.08rem;
  }
}
.upload_infobox{
  padding-bottom: 0.15rem;

  .input_text:last-child{
    border-bottom: 1px solid @border-color;
  }
}
.icon_check.long_span {
  line-height: 1.71;
  font-size: @font-base-size;
  padding-left: 0.28rem;
  color: @t-color-lightgray;
  position: absolute;
  top: 50%;
  margin-top: -0.12rem;
  right: 0.2rem;
  z-index: 50;

  &:before{
    width: 0.18rem;
    height: 0.18rem;
    font-size: 0.16rem;
    line-height: 0.16rem;
    top: 50%;
    margin-top: -0.09rem;
  }
}


/*-- 客户信息确认 --*/
.form_tit_right{
  .input_text.text{
    .t1,
    .dropdown,
    .tarea1 {
      text-align: right;
      padding-left: 1.2rem;
    }

    .error_tips{
      text-align: right;
    }
    .input_ct{
      text-align: right;
      padding-left: 1.2rem;
    }
  }
}
.mobile_module{
  margin-bottom: 0.2rem;

  .input_text.text{
    .t1,
    .dropdown,
    .tarea1{
      padding-left: 1.08rem;
    }
  }
}

.layer_box {
  width: 100%;
  background: #ffffff;
  border-radius: 0.12rem 0.12rem 0 0;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 6000;
}
.layer_tit {
  position: relative;
  text-align: center;
  height: 0.48rem;
  line-height: 0.48rem;

  h3 {
    height: 0.48rem;
    font-size: @font-sub-title-size;
    line-height: 0.48rem;
    font-weight: 500;
    text-align: center;
    margin: 0 0.5rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .close {
    display: block;
    width: 0.5rem;
    height: 0.48rem;
    line-height: 0.48rem;
    text-align: center;
    font-size: 0.2rem;
    color: @t-color-lightgray;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 50;

    &:before{
      content: "\e61d";
    }
  }
}
.layer_cont {
  height: 4.86rem;
  overflow-x: hidden;
  overflow-y: auto;
}
.select_list{
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;

    span {
      display: block;
      padding: 0.16rem 0.3rem 0.16rem 0;
      font-size: @font-form-size;
      line-height: 1.5;
      position: relative;

      em{
        display: block;
        font-size: @font-tip-size;
        line-height: 1.5;
        color: @t-color-gray;
        position: relative;
        top: 0.04rem;
      }
    }
    img{
      width: 0.24rem;
      height: 0.24rem;
      vertical-align: top;
      margin-right: 0.1rem;
    }
    &.active{
      span{
        color:@main-color;

        &:after{
          content: "\e61c";
          width: 0.24rem;
          height: 0.24rem;
          text-align: center;
          font-size: 0.24rem;
          line-height: 1;
          color: @main-color;
          font-family: "wt-iconfont" !important;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          position: absolute;
          top: 50%;
          margin-top: -0.12rem;
          right: 0;
        }
        em{
          color:@main-color;
        }
      }
    }
  }
}

/*-- 追保信息填写 --*/
.mid_title{
  background: #ffffff;
  padding: 0.12rem 0.16rem 0.13rem;
  margin-top: 0.08rem;
  font-size: @font-sub-title-size;
  line-height: 1.5;
  font-weight: 500;
  position: relative;

  em{
    display: block;
    font-size: @font-base-size;
    line-height: 1.57;
    color: @t-color-lightgray;
    margin-top: 0.02rem;
    font-style: normal;
  }

  &:after{
    content: "";
    height: 1px;
    background: @border-color;
    position: absolute;
    bottom: 0;
    left: 0.16rem;
    right: 0.16rem;
  }
}

/*-- 特殊信息申报 --*/
.test_numbox{
  padding: 0.01rem 0.05rem 0.15rem 0.15rem;
  max-height: 1.44rem;
  font-size: 0;
  line-height: 0;
  overflow: auto;
  position: relative;
  z-index: 50;

  span{
    display: inline-block;
    vertical-align: top;
    width: 0.3rem;
    height: 0.3rem;
    position: relative;
    line-height: 0.3rem;
    text-align: center;
    font-size: 0.14rem;
    color: @t-color-gray;
    border-radius: 0.04rem;
    background: #ffffff;
    margin: 0.14rem 0.05rem 0 0;
    overflow: hidden;

    &.off {
      color: @main-color;
      background: @main-color-10;

      &:after{
        content: "\e61b";
        width: 0.13rem;
        height: 0.13rem;
        text-align: right;
        line-height: 0.13rem;
        color: @main-color;
        position: absolute;
        bottom: 0;
        right: 0;
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    }
  }
}

@media (min-width: 376px) and (max-width: 413px){
  .test_numbox span{
    margin-right: 7px;
  }
}
@media (min-width: 414px) and (max-width: 599px){
  .test_numbox span{
    margin-right: 9px;
  }
}

.spel_infobox{
  background: #ffffff;
  padding: 0 0.16rem;
  margin-bottom: 0.08rem;

  .title{
    font-size: @font-sub-title-size;
    font-weight: 500;
    line-height: 1.5;
    padding: 0.12rem 0;
    border-bottom: 1px solid @border-color;
  }
}
.radio_list{
  padding: 0.08rem 0;

  .icon_radio,
  .icon_check{
    display: block;
    height: auto;
    min-height: 0.48rem;
    width: 100%;
    padding: 0.12rem 0 0.12rem 0.32rem;
    font-size: @font-form-size;
    line-height: 1.5;
    color: @t-color-gray;

    &.checked{
      color: @main-color;
    }
  }
  .icon_radio:before{
    top: 0.15rem;
    left: 0;
    margin: 0 !important;
  }
  .icon_check:before{
    width: 0.18rem;
    height: 0.18rem;
    font-size: 0.16rem;
    top: 0.15rem;
    left: 0;
    margin: 0 !important;
  }

}
.spel_info_supp{
  .mid_title{
    margin: 0.15rem 0;
    padding: 0 0.16rem;
    background: none;
    font-size: @font-base-size;
    line-height: 1.3;

    &:after{
      display: none;
    }
  }
  .input_text.text {
    .t1,
    .dropdown,
    .tarea1 {
      padding-left: 1.08rem;
    }
  }
  .com_box{
    margin-bottom: 0.08rem;
  }
}
.opea_ctbox{
  padding: 0.14rem 0;
  margin: 0 0.16rem;
  border-top: 1px solid @border-color;
  font-size: 0;
  line-height: 0;
  text-align: right;
}
.add_btn_01,
.delete_btn_01{
  display: inline-block;
  vertical-align: top;
  margin-left: 0.12rem;
  padding: 0 0.12rem;
  line-height: 2;
  font-size: @font-base-size;
  border-radius: @button-radius;
}
.add_btn_01{
  background: @link-color-10;
  color: @link-color;
}
.delete_btn_01{
  background: @button-assist-bg;
  color: @button-bg-1;
}
.unit_span{
  display: block;
  line-height: 0.56rem;
  font-size: @font-form-size;
  color: @t-color-lightgray;
  position: absolute;
  top: 0;
  right: 0;
}

/*-- 投资者教育 --*/
.p_button.countdown{
  opacity: 0.3;
}
.protocol_box{
  padding: 0.24rem 0.16rem;

  .title{
    font-size: @font-spel-size-1;
    line-height: 1.3;
    font-weight: 500;
    margin-bottom: 0.16rem;
    text-align: center;
  }
}
.protocol_cont{
  font-size: @font-form-size;
  line-height: 1.625;
}


/*-- 测评和授信 --*/
.test_box{
  background: #ffffff;
  margin-bottom: 0.08rem;
  padding: 0 0.16rem;

  .title{
    padding: 0.12rem 0;
    font-size: @font-form-size;
    line-height: 1.5;
    font-weight: 500;
    border-bottom: 1px solid @border-color;
  }

  .tips {
    padding: 0.14rem 0 0.11rem;
    font-size: @font-tip-size;
    line-height: 1.33333;
    color: @error-color;
    position: relative;
    top: -0.08rem;
  }

  .radio_list{
    .icon_radio.disabled,
    .icon_check.disabled{
      color: @t-color-lightgray;
    }
    .icon_radio.disabled:before,
    .icon_check.disabled:before{
      display: none;
    }
    .icon_radio.checked.disabled:before,
    .icon_check.checked.disabled:before{
      display: block;
    }
  }
}
.ce_btn.black{
  display: block;

  .p_button{
    margin-top: 0.12rem;
    margin-left: 0;

    &:first-child{
      margin-top: 0;
    }
  }
}
.test_result_box{
  padding: 0.32rem 0.3rem 0.26rem;
  text-align: center;

  .icon{
    height: 1.2rem;
    margin-bottom: 0.15rem;
    position: relative;

    img{
      display: block;
      height: 100%;
      margin: 0 auto;
    }
    .num{
      width: 0.9rem;
      height: 0.9rem;
      line-height: 0.9rem;
      font-size: 0.44rem;
      color: @main-color;
      position: absolute;
      top: 0.12rem;
      left: 50%;
      margin-left: -0.45rem;
    }
  }
  h5{
    font-size: @font-spel-size-2;
    line-height: 1.33333;
    font-weight: normal;
    margin-bottom: 0.1rem;

    .ok{
      color: @main-color;
    }
  }
  p{
    font-size: @font-base-size;
    line-height: 1.57;
    color: @t-color-lightgray;
  }
}
.test_info_wrap{

  .mid_title{
    margin: 0.15rem 0;
    padding: 0 0.16rem;
    background: none;
    font-size: @font-base-size;
    line-height: 1.3;

    &:after {
      display: none;
    }
  }
}
.upload_com_box2{
  margin-bottom: 0.1rem;
}
.sx_form{
  .input_text.text{
    .t1,
    .dropdown,
    .tarea1 {
      text-align: right;
      padding-left: 1.1rem;
    }
  }
}
.warn_cm_tips{
  margin: 0 0.2rem;
  border-top: 1px solid @border-color;
  color: @error-color;
  padding: 0.15rem 0;
  font-size: @font-base-size;
  line-height: 1.42;

  p{
    padding-left: 0.24rem;
    position: relative;

    &:before{
      content: "\e609";
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 1;
      color: @error-color;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      left: 0;
      top: 0.02rem;
    }
  }
}
.tips_box{
  background: #ffffff;
  margin: 0.08rem 0;
  padding: 0.15rem 0.16rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;

  p{
    display: flex;

    .num{
      display: block;
    }
  }
}


/*-- 市场及密码设置 --*/
.type_selectlist{
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    padding: 0.12rem 0 0.12rem 0.36rem;
    position: relative;
    border-bottom: 1px solid @border-color;

    &:last-child{
      border-bottom: 0 none;
    }
    h5{
      font-size: @font-sub-title-size;
      line-height: 1.5;
      font-weight: normal;
    }
    p{
      font-size: @font-base-size;
      line-height: 1.57;
      color: @t-color-lightgray;
      margin-top: 0.02rem;
    }
    .icon_check{
      padding: 0;
      width: 0.24rem;
      height: 0.24rem;
      position: absolute;
      top: 0.12rem;
      left: 0;

      &:before{
        left: 0.02rem;
      }
    }
  }
}
.bus_qxlist{
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    position: relative;

    &:last-child{
      border-bottom: 0 none;
    }

    .icon_check{
      display: block;
      padding: 0.16rem 0 0.16rem 0.36rem;
      font-size: @font-form-size;
      line-height: 1.5;
      color: @t-color-normal;

      &:before{
        left: 0.02rem;
        top: 0.18rem;
      }
    }
  }
}
.icon_eye {
  width: 0.24rem;
  height: 0.24rem;
  text-align: center;
  font-size: 0.24rem;
  line-height: 1;
  color: @border-deep-color;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 50%;
  margin-top: -0.12rem;
  right: 0;
  z-index: 50;

  &:before{
    content: "\e634";
  }

  &.show {
    color: @main-color;

    &:before{
      content: "\e633";
    }
  }
}
.pwors_select {
  background: #ffffff;
  margin: 0.08rem 0 0;
  padding: 0.16rem 0.7rem 0.16rem 0.16rem;
  line-height: 1.5;
  font-size: @font-form-size;
  position: relative;

  .switch {
    position: absolute;
    top: 0.16rem;
    right: 0.16rem;
    z-index: 50;
  }
  .imp_c_tips{
    margin-right: -0.54rem;
    border-top: 1px solid @border-color;
    position: relative;
    top: 0.16rem;
  }
}
.pwors_select + .set_pword_item .input_text:first-child{
  border-top: 1px solid @border-color;
}

.appro_result_tips{
  margin: 0 0.16rem;
  padding: 0.2rem 0;
  font-size: @font-form-size;
  background: #ffffff;
  line-height: 1.5;

  h3{
    font-size: 0.22rem;
    line-height: 1.45;
    font-weight: normal;
    text-align: center;
  }
}
.appro_ic_ok,
.appro_ic_error{
  display: block;
  position: relative;
  z-index: 5;
  width: 0.44rem;
  height: 0.44rem;
  margin: 0 auto 0.12rem;
  text-align: center;
  font-size: 0.44rem;
  line-height: 1;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.appro_ic_ok:before{
  content: "\e621";
  color: @suc-color-2;
  background: linear-gradient(142.81deg, @suc-assist-color -0.93%, @suc-color-2 100.93%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.appro_ic_error:before{
  content: "\e61e";
  color: @error-color-2;
  background: linear-gradient(143.7deg, @error-color-2 0%, @error-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.appro_warn_tips{
  background: #FFF4D2;
  border-radius: 0.08rem;
  margin: 0 0.16rem 0.2rem;
  padding: 0.1rem 0.12rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: #FFA900;
}
.appro_info{
  padding: 0 0.16rem;
  background: #ffffff;

  .item{
    display: flex;
    padding: 0.16rem 0;
    font-size: @font-form-size;
    line-height: 1.5;
    border-bottom: 1px solid @border-color;

    &:last-child{
      border-bottom: 0 none;
    }
    .tit{
      display: block;
      color: @t-color-gray;
      width: 1.7rem;
    }
    .ct{
      flex: 1;
      width: 100%;
      text-align: right;

      h5{
        font-size: @font-form-size;
        line-height: 1.5;
        font-weight: normal;
      }
    }
  }
}
.risk_info_wrap{
  padding: 0 0.16rem;
}
.risk_info_table{
  width: 100%;

  th{
    text-align: center;
    font-size: @font-form-size;
    line-height: 1.25;
    height: 0.48rem;
    vertical-align: middle;
    font-weight: normal;
    color: @t-color-lightgray;

    &:first-child{
      text-align: left;
    }
  }

  td{
    font-size: @font-form-size;
    line-height: 1.25;
    height: 0.56rem;
    text-align: center;
    vertical-align: middle;
    border-top: 1px solid @border-color;

    &:first-child{
      text-align: left;
    }
  }
}
.state_span_error{
  color: @error-color;
}
.state_span_ok{
  color: @normal-assist-color;
}
.appro_tips{
  background: #ffffff;
  margin: 0.08rem 0;
  padding: 0.16rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-gray;

  h5{
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-normal;
    font-weight: normal;
    margin-bottom: 0.08rem;
  }
}
.t_ind{
  text-indent: 2em;
}
.rule_check {
  background: #ffffff;
  margin: 0.08rem 0;
  padding: 0.15rem 0.16rem 0.15rem 0.48rem;
  position: relative;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-gray;

  .icon_check{
    padding: 0;
    width: 0.2rem;
    height: 0.2rem;
    position: absolute;
    top: 0.15rem;
    left: 0.16rem;
    z-index: 50;

    &:before{
      width: 0.18rem;
      height: 0.18rem;
      font-size: 0.16rem;
      top: 0.01rem;
      left: 0.01rem;
      margin: 0;
    }
  }

  a{
    display: block;
    color: @link-color;
    margin-top: 0.08rem;
  }
  &.spel {
    a {
      display: inline;
      margin-top: 0;
    }
  }

}

.footer{
  .rule_check{
    margin: 0;
  }
}


/*-- 三方存管 --*/
.input_form.spel .input_text:last-child {
  border-bottom: 1px solid @border-color;
}
.icon_photo {
  width: 0.24rem;
  height: 0.24rem;
  text-align: center;
  font-size: 0.22rem;
  line-height: 0.24rem;
  color: @link-color;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 50%;
  margin-top: -0.12rem;
  right: 0;
  z-index: 50;

  &:before{
    content: "\e8bc";
  }
}

.tpbank_zcbox {
  padding: 0.16rem;

  .title {
    line-height: 1.42;
    font-size: @font-base-size;
    font-weight: normal;
    color: @t-color-lightgray;
    margin-bottom: 0.08rem;
  }
}

.available_bklist {
  overflow: hidden;

  li {
    width: 33.33%;
    float: left;
    line-height: 1,42;
    padding: 0.08rem 0;
    color: @t-color-lightgray;
    font-size: @font-base-size;

    img {
      width: 0.18rem;
      height: 0.18rem;
      margin-right: 0.08rem;
      vertical-align: middle;
    }
    &:nth-child(3n) {
      text-align: right;
    }
    &:nth-child(3n + 2) {
      text-align: center;
    }
  }
}

.tpbank_img {
  padding: 0.1rem 0;
}

.tpbank_img img {
  display: block;
  width: 100%;
}

.num_layer {
  position: absolute;
  bottom: 0.58rem;
  left: 0;
  right: 0;
  z-index: 200;
  border: 1px solid @main-color;
  background: #ffffff;
  background-image: linear-gradient(90deg, @main-color-10 0%, @main-color-10 100%);
  border-radius: @module-radius-2;
  text-align: center;
  padding: 0.11rem;
  line-height: 1.4;
  font-size: @font-spel-size-1;
  color: #000000;
  font-weight: 700;
  box-shadow: 0px 0.02rem 0.04rem 0px rgba(0, 0, 0, 0.13);
}

.num_layer:before {
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 6px 0;
  border-color: @main-color transparent transparent;
  position: absolute;
  bottom: -6px;
  left: 0.85rem;
}

/*-- 见证方式 --*/
.p_video_typelist{
  padding: 0.2rem 0.16rem;

  li{
    border: 1px solid @border-color;
    border-radius: @module-radius-2;
    padding: 0.24rem 0.3rem 0.24rem 1rem;
    position: relative;
    margin-top: 0.2rem;

    &:first-child{
      margin-top: 0;
    }
    h5{
      font-size: @font-spel-size-2;
      line-height: 1.3333;
      font-weight: normal;
    }
    p{
      font-size: @font-base-size;
      line-height: 1.42;
      color: @t-color-lightgray;
      margin-top: 0.04rem;
    }

    i{
      display: block;
      width: 0.48rem;
      height: 0.48rem;
      position: absolute;
      top: 50%;
      margin-top: -0.24rem;
      left: 0.25rem;

      &.ic01{
        background: url(../images/video_icon_01.png) no-repeat center;
        background-size: 100%;
      }
      &.ic02{
        background: url(../images/video_icon_02.png) no-repeat center;
        background-size: 100%;
      }
    }
    &.active{
      border-color: @main-color;
      background: @main-color-10;
    }
  }
}

/*-- 预约信息填写 --*/
.icon_location {
  line-height: 1.71;
  padding: 0.16rem 0;
  font-size: @font-base-size;
  color: @link-color;
  padding-left: 0.26rem;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 50;

  &:before {
    content: "\e677";
    width: 0.24rem;
    height: 0.24rem;
    font-size: 0.24rem;
    line-height: 1;
    color: @link-color;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    margin-top: -0.12rem;
    left: 0;
  }
}

.p_r70 {
  padding-right: 0.7rem !important;
}

.imp_c_tips {
  padding: 0.15rem 0;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @text-tip-color;

  p {
    padding-left: 0.24rem;
    position: relative;

    &:before {
      content: "\e654";
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 1;
      color: @text-tip-color;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      top: 0.02rem;
      left: 0;
    }
    &.warn{
      color: @imp_color;

      &:before{
        color: @imp_color;
      }
    }
  }

  .imp {
    color: @imp_color;
  }
}
.qx_tipbox {
  text-align: left;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-gray;
  padding: 0.14rem 0 0.1rem 0.48rem;
  position: relative;

  .icon {
    width: 0.4rem;
    height: 0.4rem;
    position: absolute;
    top: 0.14rem;
    left: 0;

    img {
      display: block;
      width: 100%;
    }
  }
  h5 {
    font-size: @font-sub-title-size;
    line-height: 1.5;
    margin-bottom: 0.04rem;
    font-weight: 500;
    color: #000000;
  }
}
.yyb_info_text {
  padding: 0 0.3rem 0.12rem 0.88rem;
  font-size: @font-tip-size;
  line-height: 1.5;
  color: @t-color-gray;
  position: relative;
  top: -0.06rem;

  p {
    margin-top: 0.04rem;
    padding-left: 0.16rem;
    position: relative;

    &:first-child {
      margin-top: 0;
    }
    i {
      display: block;
      width: 0.11rem;
      height: 0.12rem;
      position: absolute;
      top: 0.03rem;
      left: 0;

      &.addr {
        background: url(../images/yyb_ic_addr.png) no-repeat center;
        background-size: 100%;
      }
      &.tel {
        background: url(../images/yyb_ic_tel.png) no-repeat center;
        background-size: 100%;
      }
    }
  }
}
.sele_lyinfo {
  height: 0.56rem;
  padding: 0.11rem 0 0.11rem 0.16rem;
  font-size: @font-sub-title-size;
  color: @main-color;
  display: flex;

  span {
    display: block;
    margin-right: 0.1rem;
    border: 1px solid @main-color;
    padding: 0.06rem 0.07rem;
    font-size: @font-sub-title-size;
    border-radius: 0.02rem;
    line-height: 1.25;
    color: @main-color;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &.active {
      color: @main-color;
      border-color: @main-color;
    }
    &.off {
      padding-right: 0.16rem;
      min-width: 0.7rem;
      color: #000000;
      border: 1px solid #E5E5E5;

      &:after {
        content: "";
        width: 0.07rem;
        height: 0.07rem;
        border-top: 1px solid #000000;
        border-right: 1px solid #000000;
        position: absolute;
        top: 50%;
        margin-top: -0.04rem;
        right: 0.07rem;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
      }
    }
  }
}
.sele_lyinfo + .layer_cont,
.sele_lyinfo + .layer_cont.fixed{
  height: 4.3rem;
}
.network_list {
  padding: 0 0.16rem;

  li {
    border-bottom: 1px solid @border-color;
    position: relative;
    padding: 0.15rem 0.56rem 0.15rem 0;

    h5 {
      padding-left: 0.22rem;
      line-height: 1.5;
      font-size: @font-sub-title-size;
      font-weight: normal;
      position: relative;

      &:before {
        content: "\e677";
        width: 0.16rem;
        height: 0.16rem;
        font-size: 0.16rem;
        line-height: 1;
        color: @t-color-normal;
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        position: absolute;
        top: 50%;
        margin-top: -0.08rem;
        left: 0;
      }
    }
    p {
      padding-left: 0.22rem;
      font-size: @font-tip-size;
      color: @t-color-lightgray;
      line-height: 1.5;
      margin-top: 0.04rem;
    }
    &.active{
      &:after {
        content: "\e61c";
        width: 0.24rem;
        height: 0.24rem;
        text-align: center;
        font-size: 0.24rem;
        line-height: 1;
        color: @main-color;
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        position: absolute;
        top: 50%;
        margin-top: -0.12rem;
        right: 0;
      }
      h5,
      p {
        color: @main-color;
      }
      h5:before {
        color: @main-color;
      }
    }
  }
}
.query_ctbox{
  padding-bottom: 0.16rem;

  h5 {
    font-size: @font-base-size;
    color: @t-color-gray;
    font-weight: normal;
    line-height: 1.42;
    padding: 0.1rem 0;
  }
}
.query_list{
  height: 2.8rem;
  overflow: auto;

  li {
    padding: 0.1rem 0 0.1rem 0.24rem;
    font-size: @font-base-size;
    line-height: 1.71;
    position: relative;
    border-bottom: 1px solid @border-color;


    .ic_dw,
    .ic_dz {
      display: inline-block;
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 1;
      color: @t-color-gray;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      left: 0;
    }
    .ic_dw:before{
      content: "\e635";
    }
    .ic_dz:before{
      content: "\e677";
    }
    em{
      display: block;
      font-size: @font-tip-size;
      line-height: 1.33333;
      color: @t-color-lightgray;
    }
  }
  &.dw{
    li{
      border-bottom: 0 none;
      padding-top: 0.07rem;
      padding-bottom: 0.07rem;
      font-size: @font-form-size;
      line-height: 1.5;
    }
  }
}
.key_word {
  color: @main-color !important;
}
.form_tit_right{
  .input_text.text{
    .t1:focus,
    .tarea1:focus,
    .tarea1.focus {
      text-align: left;
      padding-right: 0.36rem;
      padding-left: 0.88rem;
    }
  }
}

/*-- 结果页 --*/
.result_page{
  background: #ffffff;
}
.result_tips{
  padding: 0.32rem 0.5rem;
  text-align: center;

  .icon{
    display: block;
    width: 0.56rem;
    height: 0.56rem;
    font-size: 0.56rem;
    border-radius: 50%;
    line-height: 1;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0 auto 0.3rem;
    position: relative;
    z-index: 10;


    &.ing{
      color: @suc-color-2;
      &:before{
        content: "\e628";
        color: #4A8AF7;
        background: linear-gradient(143.7deg, #91C1FA 0%, #4A8AF7 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &.ok{
      color: @suc-color-2;
      &:before{
        content: "\e621";
        color: @suc-color-2;
        background: linear-gradient(142.81deg, @suc-assist-color -0.93%, @suc-color-2 100.93%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &.fail{
      color: @error-color;
      &:before{
        content: "\e61e";
        color: @error-color;
        background: linear-gradient(143.7deg, @error-color-2 0%, @error-color 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &.reject{
      color: @error-color;
      &:before{
        content: "\e61e";
        color: @error-color;
        background: linear-gradient(143.7deg, @error-color-2 0%, @error-color 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &.vedio_ok{
      color: @suc-color-2;
      &:before{
        content: "\e621";
        color: @suc-color-2;
        background: linear-gradient(142.81deg, @suc-assist-color -0.93%, @suc-color-2 100.93%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &.vedio_error{
      color: @error-color;
      &:before{
        content: "\e61e";
        color: @error-color;
        background: linear-gradient(143.7deg, @error-color-2 0%, @error-color 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &.vedio_ok + h5,
    &.vedio_error + h5{
      margin-bottom: 0.12rem;
    }
  }
  h5{
    font-size: @font-spel-size-2;
    line-height: 1.33333;
    font-weight: normal;
  }
  p{
    font-size: @font-base-size;
    line-height: 1.3;
    color: @t-color-lightgray;
    margin-top: 0.06rem;
  }
}
.result_step {
  padding: 0 0 0.25rem;
  position: relative;

  ul{
    display: flex;

    li {
      flex: 1;
      position: relative;
      text-align: center;

      &:before {
        content: '';
        width: 100%;
        height: 1px;
        background: #EAEAEF;
        position: absolute;
        left: -50%;
        top: 0.15rem;
      }
      &:first-child:before {
        display: none;
      }
      i {
        display: block;
        width: 0.3rem;
        height: 0.3rem;
        margin: 0 auto 0.02rem;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 0.3rem 0.68rem;
        background-color: #ffffff;
        border-radius: 50%;
        position: relative;
        z-index: 100;
      }
      &.s1 i{
        background-image: url(../images/step_ic01.png);
      }
      &.s2 i{
        background-image: url(../images/step_ic02.png);
      }
      &.s3 i{
        background-image: url(../images/step_ic03.png);
      }
      &.s4 i{
        background-image: url(../images/step_ic04.png);
      }
      span {
        display: block;
        line-height: 1.42;
        color: @t-color-gray;
      }
      &.off:before,
      &.on:before {
        background: @main-color;
      }
      &.off i,
      &.on i {
        background-position: 0 bottom;
      }
    }
  }
}
.result_info{
  padding: 0 0.16rem;

  ul{
    border-top: 1px solid @border-color;

    li{
      border-bottom: 1px solid @border-color;
      display: flex;
      padding: 0.16rem 0;
      font-size: @font-form-size;
      line-height: 1.5;

      .tit{
        display: block;
        min-width: 1rem;
      }
      p{
        flex: 1;
        color: @t-color-gray;
        text-align: right;

        &.error{
          color: @error-color;
        }
      }
      .state{
        color: @t-color-lightgray;

        &.error{
          color: @error-color;
        }
      }
    }
  }
}
.result_page + .tips_box{
  margin: 0;
  padding: 0.2rem 0.16rem 0;
}
.icon_local{
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.05rem;
  width: 0.2rem;
  height: 0.2rem;
  font-size: 0.2rem;
  line-height: 1;
  color: @link-color;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &:before{
    content: "\e677";
  }
}
.reject_txtinfo {
  background: #ffffff;
  border-top: 1px solid @border-color;
  padding: 0.2rem 0 0.1rem;
  margin: 0 0.16rem;
  line-height: 1.57;
  font-size: @font-base-size;
  color: #000000;

  .title {
    font-weight: normal;
    color: @t-color-lightgray;
    font-size: @font-sub-title-size;
    line-height: 1.5;
    margin-bottom: 0.08rem;
  }
  p {
    padding: 0.03rem 0;
  }
}

/*-- 面见人登录 --*/
.layer_box.spel{
  .layer_cont{
    height: auto;
    max-height: 4.86rem;
  }
  .layer_tit{
    border-bottom: 1px solid @border-color;
  }
}
.ly_opea_cancel,
.ly_opea_sure{
  padding: 0 0.16rem;
  font-size: @font-form-size;
  line-height: 0.48rem;
  position: absolute;
  top: 0;
  z-index: 50;
}
.ly_opea_cancel{
  color: @t-color-normal;
  left: 0;
}
.ly_opea_sure{
  color: @main-color;
  right: 0;
}
.am_loginform{
  padding: 0.28rem 0.16rem 0.01rem;
  min-height: 2.4rem;

  .input_text:last-child{
    border-bottom: 1px solid @border-color;
  }
}

/*-- 视频见证 --*/
.video_tippage {
  padding: 0.36rem 0.16rem 0.25rem;
  background: #fff;
  margin-bottom: 0.1rem;

  .title{
    text-align: center;
    line-height: 1.42;
    color: @t-color-lightgray;

    h3 {
      font-size: @font-spel-size-2;
      line-height: 1.3333;
      margin-bottom: 0.12rem;
      font-weight: normal;
      color: @t-color-normal;

      b{
        font-weight: normal;
        color: @main-color;
      }
    }
  }

}
.witness_list {
  margin-top: 0.15rem;
  padding: 0 0.14rem;

  li {
    width: 100%;
    display: table;
    text-align: left;
    vertical-align: middle;

    .icon {
      display: table-cell;
      width: 0.44rem;
      vertical-align: middle;
      font-size: 0.44rem;
      line-height: 1;
      text-align: center;
      color: @suc-color-2;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;

      &:before{
        color: @suc-color-2;
      }
      img {
        display: none;
        width: 0.44rem;
        height: 0.44rem;
      }
    }
    &:nth-child(1) .icon:before{
      content: "\e625";
    }
    &:nth-child(2) .icon:before{
      content: "\e626";
    }
    &:nth-child(3) .icon:before{
      content: "\e622";
    }
    &:nth-child(4) .icon:before{
      content: "\e624";
    }


    p {
      display: table-cell;
      height: 0.84rem;
      padding: 0.1rem 0 0.1rem 0.2rem;
      line-height: 1.25;
      font-size: @font-form-size;
      color: @t-color-normal;
      vertical-align: middle;

      em {
        font-size: @font-base-size;
        display: block;
        font-style: normal;
        color: @imp_color;
        margin-top: 0.02rem;
      }
    }

  }
}
.waiting_box {
  background: #ffffff;
  padding: 0.5rem 0.2rem 0.2rem;
  min-height: 3.1rem;
  margin-bottom: 0.08rem;
  text-align: center;
  line-height: 1.42;
  color: @t-color-gray;

  h5 {
    font-size: @font-spel-size-2;
    line-height: 1.33333;
    font-weight: normal;
    color: @t-color-normal;
    margin-bottom: 0.12rem;
  }
}
.queue_level {
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 auto 0.3rem;
  position: relative;

  .bg {
    display: block;
    width: 1.2rem;
    height: 1.2rem;
    text-align: center;
    font-size: 1.2rem;
    line-height: 1.2rem;
    color: @main-color-30;
    border-radius: 100%;
    background: #ffffff;
    position: absolute;
    top: 0;
    left: 0;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-animation: allrotate 1.6s infinite linear;
    animation: allrotate 1.6s infinite linear;

    &:before{
      content: "\e6e9";
    }
  }
  .pic {
    width: 100%;
    height: 100%;
    text-align: center;
    font-size: 0.6rem;
    line-height: 1.2rem;
    color: @main-color;
    border-radius: 100%;
    background: @main-color-3;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;

    &:before{
      content: "\e627";
    }

    img {
      display: none;
      width: 100%;
      height: 100%;
    }
  }
  .num {
    width: 100%;
    text-align: center;
    height: 1.2rem;
    line-height: 1.2rem;
    font-size: 0.62rem;
    border-radius: 100%;
    background: @main-color-3;
    color: @main-color;
    font-weight: 500;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;
  }
}

@-webkit-keyframes allrotate {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes allrotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/*-- 资料上传 --*/
.file_cmlist{
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    position: relative;
    display: flex;
    padding-right: 0.2rem;
    font-size: @font-form-size;
    line-height: 1.5;
    align-items: center;

    &:last-child{
      border-bottom: 0 none;
    }
    &:after{
      content: "\e619";
      width: 0.16rem;
      height: 0.16rem;
      text-align: center;
      font-size: 0.16rem;
      line-height: 1;
      color: @t-color-lightgray;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: -0.02rem;
    }
    a{
      display: block;
      padding: 0.16rem 0;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: @t-color-normal;
    }
    .state{
      display: block;
      font-size: @font-base-size;
      line-height: 1.57;
      margin-left: 0.16rem;
      color: @t-color-lightgray;

      &.error{
        color: @error-color;
      }
    }
  }
}
.file_upload_item{
  background: #ffffff;

  .pic{
    padding: 0.2rem 0.3rem;

    img{
      display: block;
      width: 100%;
      border: 1px solid @border-color;
    }
    &.spel img{
      border: 0 none;
      width: 2.5rem;
      margin: 0 auto;
    }
  }
  .btn{
    border-top: 1px solid @border-color;
    display: block;
    line-height: 3;
    text-align: center;
    font-size: @font-form-size;
    color: @button-bg-1;
  }
}
.imp_tiptxt{
  margin: 0.08rem 0;
  padding: 0.15rem 0.16rem 0.15rem 0.4rem;
  background: #ffffff;
  position: relative;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;

  i{
    display: block;
    width: 0.16rem;
    height: 0.16rem;
    font-size: 0.16rem;
    line-height: 1;
    color: @t-color-lightgray;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 0.17rem;
    left: 0.16rem;

    &:before{
      content: "\e654";
    }
  }
}
.com_tips {
  background: #ffffff;
  margin: 0.08rem 0;
  padding: 0.14rem 0.16rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-gray;
}
.link_right_arrow {
  display: inline-block;
  color: @link-color;
  position: relative;
  padding-right: 0.16rem;

  &:after {
    content: "\e619";
    width: 0.12rem;
    height: 0.12rem;
    color: @link-color;
    text-align: center;
    font-size: 0.12rem;
    line-height: 1;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    margin-top: -0.06rem;
    right: 0;
  }
}
.info_form_wrap .com_box{
  margin: 0.08rem 0;
}


/*-- 签署密码和协议 --*/
.wx_tips{
  margin: 0.15rem 0.16rem;
  color: @t-color-lightgray;
  font-size: @font-base-size;
  line-height: 1.42;

  p{
    margin-top: 0.06rem;

    &:first-child{
      margin-top: 0;
    }
  }

  ul{
    margin: 0.08rem 0;

    li {
      position: relative;
      padding-left: 0.1rem;

      &:before {
        content: '';
        width: 0.03rem;
        height: 0.03rem;
        background: @t-color-lightgray;
        border-radius: 50%;
        position: absolute;
        top: 0.1rem;
        left: 0;
      }
    }
  }
}
.protocol_list{
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    position: relative;

    a{
      display: block;
      padding: 0.16rem 0.5rem 0.16rem 0;
      font-size: @font-form-size;
      line-height: 1.5;
      color: @t-color-normal;
      position: relative;

      &:after{
        content: "\e619";
        width: 0.14rem;
        height: 0.14rem;
        text-align: center;
        font-size: 0.14rem;
        line-height: 1;
        color: @t-color-lightgray;
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        position: absolute;
        top: 50%;
        margin-top: -0.07rem;
        right: -0.02rem;
      }
    }
    .state{
      font-size: @font-base-size;
      line-height: 1.71;
      position: absolute;
      top: 50%;
      right: 0.2rem;
      color: @error-color;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
    &.readed{
      a{
        color: @t-color-gray;
      }
      .state{
        color: #BBBBBB;
      }
    }
  }
}




/*-- 活体检测 --*/
.fc_basebox {
  background: #fff;
  padding: 0.34rem 0.25rem 0.1rem;
  text-align: center;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;

  h5 {
    font-size: @font-spel-size-2;
    line-height: 1.33333;
    font-weight: normal;
    margin-bottom: 0.12rem;
    color: @t-color-normal;
  }
  .pic {
    margin-top: 0.3rem;

    img {
      display: block;
      width: 100%;
    }
  }
}
.lz_tipbox {
  background: #fff;
  padding: 0.1rem 0.25rem;
  margin-bottom: 0.08rem;

  .title {
    margin-bottom: 0.14rem;
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-lightgray;
    font-weight: normal;
  }
  ul {
    width: 100%;
    display: flex;

    li {
      flex: 1;
      text-align: center;

      i {
        display: block;
        width: 0.36rem;
        height: 0.36rem;
        margin: 0 auto 0.05rem;

        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      span {
        display: block;
        font-size: @font-base-size;
        line-height: 1.42;
        color: @t-color-lightgray;
      }
    }
  }
}
.video_main {
  width: 100%;
  background: #000000;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
}

.common_video {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  video {
    width: 100%;
    height: 100%;
  }
}

.video_flex_wrap {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 200;
}

.video_flex_head,
.video_flex_top,
.video_flex_bottom,
.video_flex_middle:before,
.video_flex_middle:after {
  background: rgba(0, 0, 0, 0.7);
}

.video_flex_head {
  display: block;
  height: 0.44rem;
  width: 100%;
  position: relative;
}

.video_flex_top {
  height: 1.5rem;

  .table_wrap {
    width: 100%;
    display: table;
    height: 100%;
  }
  .table_td {
    height: 100%;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    padding: 0 0.2rem;
  }
}

.video_flex_middle {
  width: 100%;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: relative;

  &:before,
  &:after {
    content: "";
    display: block;
    height: 3.44rem;
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    width: 100%;
  }
}
.portrait_line {
  display: block;
  width: 3.04rem;
  height: 3.44rem;
  background: url(../images/portrait_icon3.png) no-repeat center;
  background-size: 100%;
  position: relative;

  &:before {
    content: "";
    width: 100%;
    height: 100%;
    background: url(../images/portrait_icon4.png) no-repeat center;
    background-size: 100%;
    position: absolute;
    top: 0;
    left: 0
  }
}


.video_flex_bottom {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  width: 100%;

  .table_wrap {
    height: 100%;
    width: 100%;
    display: table;

    .table_td {
      height: 100%;
      display: table-cell;
      vertical-align: middle;
      text-align: center;
      padding: 0 0.2rem;
    }
  }
}
.video_errortips {
  width: 100%;
  text-align: center;
  position: fixed;
  top: 38%;
  left: 0;
  z-index: 200;

  span {
    display: inline-block;
    padding: 0.05rem 0.17rem;
    text-align: center;
    background: #fe3b30;
    border-radius: 0.03rem;
    font-size: @font-spel-size-1;
    line-height: 1.5;
    color: #fff;
  }
}

.video_flex_middle .video_errortips {
  padding: 0 0.25rem;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);

  span {
    background: #FD4D43;
    padding: 0.08rem 0.15rem;
    font-size: @font-title-size;
    line-height: 1.33333;
    color: #EAEAEA;
    border-radius: 0.1rem;
  }
}
.video_flex_wrap.spel .video_flex_head {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
  background: none;
}

.video_flex_wrap.spel .video_flex_top {
  flex: 1;
  padding-top: 0.44rem;
  height: auto;
}

.v_opea_tips {
  display: block;
  padding: 0.08rem 0.15rem;
  text-align: center;
  color: #ffffff;
  font-size: @font-title-size;
  line-height: 1.333333;
  background: rgba(48, 48, 48, 0.502826);
  border: 1px solid rgba(122, 122, 122, 0.396939);
  border-radius: 0.08rem;
}

.back_btn {
  display: block;
  width: 0.32rem;
  height: 0.32rem;
  text-align: center;
  font-size: 0.2rem;
  line-height: 0.32rem;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.20);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 50%;
  position: absolute;
  top: 0.06rem;
  left: 0.1rem;
  z-index: 50;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &:before{
    content: "\e61a";
  }
}
.video_loading {
  width: 1.6rem;
  padding: 0.15rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: @module-radius-2;
  text-align: center;
  font-size: @font-form-size;
  line-height: 1.25;
  color: #51B4FE;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -0.8rem;
  -webkit-transform: translateY(-100%);
  transform: translateY(-100%);
  z-index: 200;

  .pic {
    height: 0.72rem;
    margin-bottom: 0.25rem;

    img {
      display: block;
      height: 100%;
      margin: 0 auto;
    }
  }
}
.face_icon {
  height: 0.9rem;
  position: relative;
  margin: 0 auto;

  img {
    display: block;
    height: 100%;
    margin: 0 auto;
  }
}

.network_state {
  line-height: 1.66666;
  font-size: @font-tip-size;
  color: #ffffff;
  position: absolute;
  top: 0.12rem;
  right: 0.1rem;
  z-index: 200;

  .error {
    color: #FF4951;
  }
  .warn {
    color: #FFBE00;
  }
  .ok {
    color: #17D744;
  }
}
.fc_sbbox {
  background: #fff;
  padding: 0.6rem 0.15rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;
  text-align: center;

  h5 {
    font-size: @font-spel-size-2;
    line-height: 1.33333;
    font-weight: normal;
    color: #333;
    margin-bottom: 0.1rem;
  }
}
.fc_imgbox {
  width: 2.97rem;
  height: 2.97rem;
  margin: 0 auto 0.4rem;
  padding: 0.21rem;
  position: relative;

  &:before{
    content: "";
    width: 100%;
    height: 100%;
    background: url(../images/fc_bg01.png) no-repeat center;
    background-size: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  &.ing:before{
    -webkit-animation: allrotate 1s infinite linear;
    animation: allrotate 1s infinite linear;
  }
  .pic {
    border-radius: 100%;
    width: 2.55rem;
    height: 2.55rem;
    overflow: hidden;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }
}
.video_compage {
  min-height: 100%;
  position: relative;
  background: #fff;
  padding-top: 0.32rem;
}

.review_video {
  width: 2.96rem;
  height: 3.94rem;
  margin: 0 auto 0.15rem;
  position: relative;
  box-shadow: 0 0.08rem 0.25rem rgba(0, 0, 0, 0.2);
  border-radius: @module-radius-2;
  overflow: hidden;

  .pic {
    width: 100%;
    height: 100%;
    background: #eeeeee;
    overflow: hidden;

    img {
      display: block;
      width: 100%;
      position: relative;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
  .window {
    width: 100%;
    height: 100%;
    position: relative;
    background: #000000;

    video {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .btn {
    width: 0.64rem;
    text-align: center;
    font-size: @font-form-size;
    line-height: 1.375;
    padding-top: 0.6rem;
    color: #fff;
    text-shadow: 0 0.02rem 0.04rem rgba(0, 0, 0, 0.77);
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -0.32rem;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 50;

    &:before {
      content: '';
      width: 0.52rem;
      height: 0.52rem;
      border-radius: 100%;
      background: rgba(0, 0, 0, 0.6) url(../images/review_play.png) no-repeat center;
      background-size: 0.3rem;
      position: absolute;
      top: 0;
      left: 50%;
      margin-left: -0.26rem;
    }
    &.on:before {
      background-image: url(../images/review_stop.png);
    }
  }
}

.vd_result_box {
  background: rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 0.12rem 0.16rem;
  font-size: @font-form-size;
  line-height: 1.5;
  min-height: 0.48rem;
  color: #ffffff;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 50;
  .ok,
  .error {
    display: inline-block;
    vertical-align: top;
    position: relative;
    padding-left: 0.28rem;
    text-align: left;
  }
  .ok:after,
  .error:after{
    content: "";
    width: 0.16rem;
    height: 0.16rem;
    border-radius: 50%;
    background: #ffffff;
    position: absolute;
    top: 0.04rem;
    left: 0.02rem;
    z-index: 5;
  }
  .ok:before,
  .error:before {
    content: "";
    width: 0.2rem;
    height: 0.2rem;
    font-size: 0.2rem;
    line-height: 1;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 0.02rem;
    left: 0;
    z-index: 10;
  }
  .ok:before {
    content: "\e621";
    color: @suc-assist-color;
  }

  .error:before {
    content: "\e61e";
    color: @error-color;
  }
}
.video_info {
  padding: 0.1rem 0.16rem;

  h5 {
    text-align: center;
    font-weight: normal;
    font-size: @font-spel-size-1;
    line-height: 1.4;
    margin-bottom: 0.16rem;
  }
  ul {
    width: 2.96rem;
    margin: 0 auto;

    li {
      padding: 0.04rem 0 0.04rem 0.24rem;
      position: relative;
      font-size: @font-form-size;
      line-height: 1.385;

      i {
        width: 0.08rem;
        height: 0.08rem;
        border-radius: 50%;
        background: @main-color;
        position: absolute;
        top: 0.11rem;
        left: 0.07rem;
      }
      &.tip_error i,
      &.tip_ok i {
        width: 0.16rem;
        height: 0.16rem;
        font-size: 0.16rem;
        line-height: 1;
        background: none;
        border-radius: 0;
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        top: 0.07rem;
        left: 0;
      }
      &.tip_error i {
        color: @error-color;

        &:before{
          content: "\e609";
        }
      }

      &.tip_ok i {
        color: @suc-color;

        &:before{
          content: "\e651";
        }
      }
    }
  }
}
.notice_box {
  background: #ffffff;
  padding: 0.34rem 0.16rem 0.2rem;
  min-height: 2.8rem;
  text-align: center;
  color: @t-color-lightgray;
  line-height: 1.42;

  .pic {
    width: 1.2rem;
    height: 1.2rem;
    margin: 0 auto 0.04rem;

    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  h5 {
    font-size: @font-spel-size-2;
    font-weight: normal;
    line-height: 1.3333;
    margin-bottom: 0.12rem;
    color: @t-color-normal;

    &.error {
      padding-top: 0.3rem;
      color: @error-color;
    }
  }
  p a {
    color: @link-color;
  }
  &.spel {
    padding-top: 0.5rem;
  }
}
.file_upload_item{
  position: relative;
}
.up_loading{
  background: rgba(0,0,0,0.6);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0.48rem;
  z-index: 100;
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);

  .wrap{
    width: 100%;
    padding: 0 0.2rem;
    font-size: @font-form-size;
    line-height: 1.375;
    text-align: center;
    color: #ffffff;
    position: absolute;
    top: 50%;
    left: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  .icon{
    display: block;
    width: 0.48rem;
    height: 0.48rem;
    background: url(../images/up_loading_icon.png) no-repeat center;
    background-size: 100%;
    margin: 0 auto 0.1rem;
    -webkit-animation: allrotate 1s infinite linear;
    animation: allrotate 1s infinite linear;
  }
}


/*-- 修改手机号  --*/
.phone_num_page{
  padding: 0.3rem 0;

  .input_form{
    .input_text:last-child{
      border-bottom: 1px solid @border-color;
    }
  }
  .ce_btn{
    margin-top: 0.33rem;
  }
}
.phone_info{
  padding: 0 0.16rem 0.24rem;
  text-align: center;

  .icon{
    width: 0.6rem;
    height: 0.6rem;
    margin: 0 auto 0.2rem;
    text-align: center;
    line-height: 0.6rem;
    font-size: 0.6rem;
    color: @t-color-lightgray;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &:before{
      content: "\e620";
    }
  }

  p{
    font-size: @font-base-size;
    line-height: 1.42;
    color: @text-tip-color;
  }
  .num{
    font-size: @font-form-size;
    line-height: 1.5;
  }
}



/*-- 风险测评 test --*/
.test_rsult {
  background: #ffffff;
  padding: 0 0.16rem;

  .appro_info {
    padding: 0.2rem 0;
    border-bottom: 1px solid @border-color;

    ul li {
      margin-top: 0.2rem;

      &:first-child {
        margin-top: 0;
      }
      .tit {
        display: block;
        color: @t-color-lightgray;
        font-size: @font-base-size;
        line-height: 1.42;
      }
      p {
        font-size: @font-form-size;
        line-height: 1.5;
        color: @t-color-normal;
      }
    }
  }
  .appro_tips{
    margin: 0;
    padding: 0.2rem 0;
    font-size: @font-base-size;
    line-height: 1.57;
    color: @t-color-lightgray;
  }
}

.test_infobox {
  padding: 0.2rem 0;
  border-bottom: 1px solid @border-color;
}

.test_inner {
  position: relative;
  display: flex;

  .info {
    font-size: @font-form-size;
    line-height: 1.375;
    flex: 1;
    width: 100%;

    h5 {
      font-size: 0.3rem;
      font-weight: 500;
      line-height: 0.36rem;
      color: @main-color;
      margin-bottom: 0.14rem;
    }
  }
}

.test_level {
  width: 1.3rem;
  height: 1.3rem;
  text-align: center;
  overflow: hidden;
  position: relative;
  margin-left: 0.3rem;

  .txt{
    width: 100%;
    position: absolute;
    top: 0.4rem;
    left: 0;
    z-index: 50;

    h5 {
      height: 0.44rem;
      line-height: 0.44rem;
      font-size: @font-base-size;
      font-weight: normal;
      color: @main-color;

      strong {
        font-size: 0.32rem;
        margin-right: 0.02rem;
      }
    }
    p {
      font-size: @font-base-size;
      color: @t-color-lightgray;
      line-height: 1.42;
    }
  }
}
.test_canvas canvas {
  width: 1.3rem;
  height: 1.3rem;
  margin: 0 auto;
  display: block;
}


/*-- 上传身份证  --*/
.upload_com_box.spel{
  margin-bottom: 0;

  .upload_wrap{
    padding: 0.16rem 0.16rem;
  }
  .upload_item {
    .pic{
      position: relative;
      background: @bg-color;
      border-radius: @module-radius;
      overflow: hidden;
      padding-top: 67.5%;

      img{
        display: block;
        width: calc(100% - 0.24rem);
        height: calc(100% - 0.24rem);
        position: absolute;
        top: 0.12rem;
        left: 0.12rem;
        -webkit-transform: translateY(0);
        transform: translateY(0);
      }
    }
    .btn{
      padding-top: 0;
      height: auto;
      position: relative;
      margin-top: 0.08rem;
      display: block;
      text-align: center;
      font-size: @font-base-size;
      border-radius: @module-radius;
      line-height: 2.42;
      background: @button-bg-1;
      color: #ffffff;

      &:before{
        display: none;
      }
    }

    .reset_btn{
      padding: 0;
      height: auto;
      position: relative;
      bottom: 0;
      left: 0;
      -webkit-transform: translateX(0);
      transform: translateX(0);
      margin-top: 0.08rem;
      display: block;
      text-align: center;
      font-size: @font-base-size;
      border-radius: @module-radius;
      line-height: 2.42;
      background: @button-assist-bg;
      color: @button-bg-1;
    }


  }

  .photo_tips{
    padding-top: 0;
    .title{
      font-size: @font-sub-title-size;
      line-height: 1.5;
      color: @t-color-normal;
      margin-bottom: 0.12rem;
      padding: 0;
    }
    .list{
      margin: 0.12rem 0 0;

      li{
        span{
          font-size: @font-base-size;
          color: @t-color-gray;
        }
      }
    }
  }
}

.help_txtbox {
  padding: 0.18rem 0.3rem 0.3rem;
  font-size: @font-form-size;
  line-height: 1.5;
  color: @t-color-normal;

  &:first-child {
    padding-top: 0;
  }
  .title {
    position: relative;
    text-align: center;
    font-size: @font-spel-size-2;
    line-height: 1.33333;
    font-weight: normal;
    margin-bottom: 0.08rem;
    color: @main-color;

    span {
      position: relative;
      z-index: 10;
      padding: 0 0.54rem;

      &:before,
      &:after{
        content: "";
        display: block;
        width: 0.42rem;
        height: 0.06rem;
        background: url(../images/tit_ic_bg.png) no-repeat center;
        background-size: 100%;
        position: absolute;
        top: 50%;
        margin-top: -0.03rem;
      }
      &:before{
        left: 0;
      }

      &:after{
        right: 0;
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg);
      }
    }
  }
  p {
    margin: 0.08rem 0;
  }
  .img_item {
    margin-top: 0.16rem;

    img {
      display: block;
      width: 100%;
    }
  }
}


/*--  重置密码 --*/
.info_compage{
  .input_form{
    .input_text:last-child {
      border-bottom: 1px solid @border-color;
    }
  }
}
.pword_tips {
  padding: 0.16rem;
  background: #ffffff;
  margin: 0.08rem 0;
  color: @t-color-lightgray;
  line-height: 1.71;

  ul li {
    position: relative;
    padding-left: 0.1rem;

    &:before {
      content: '';
      width: 0.03rem;
      height: 0.03rem;
      background: @t-color-lightgray;
      -moz-border-radius: 100%;
      -webkit-border-radius: 100%;
      border-radius: 100%;
      position: absolute;
      top: 0.1rem;
      left: 0;
    }
  }
}


/*-- 三方存管 --*/
.nodata_box{
  padding: 0.8rem 0.2rem;
  text-align: center;

  .icon{
    display: block;
    width: 0.64rem;
    height: 0.64rem;
    margin: 0 auto 0.08rem;

    img{
      display: block;
      width: 100%;
    }
    svg{
      display: block;
      width: 100%;
      fill: @disbaled-bg !important;
    }
  }
  p{
    font-size: @font-base-size;
    line-height: 1.4;
    color: @disbaled-bg;
  }
}

.tp_mid_title{
  padding: 0 0.16rem;
  margin: 0.12rem 0;

  h5{
    font-size: @font-sub-title-size;
    line-height: 1.5;
    font-weight: 500;
    color: @t-color-normal;

    a{
      font-size: @font-base-size;
      display: inline-block;
      color: @link-color;
      margin-left: 0.1rem;
    }
  }
  p{
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-lightgray;
    margin-top: 0.04rem;
  }
}
.p_tpbank_list{
  margin: 0.12rem 0.16rem 0.2rem;
}
.p_tpbank_card{
  margin-top: 0.08rem;
  border-radius: @module-radius-2;
  position: relative;
  background: @bg-color;

  .tp_bank_info{
    background-image: -webkit-linear-gradient(270deg, #626E9E 0%, #49517A 100%);
    background-image: -moz-linear-gradient(270deg, #626E9E 0%, #49517A 100%);
    background-image: -o-linear-gradient(270deg, #626E9E 0%, #49517A 100%);
    background: linear-gradient(270deg, #626E9E 0%, #49517A 100%);
  }


  &.zhu{

    .tp_bank_info{
      background-image: -webkit-linear-gradient(270deg, #F3575D 0%, #CA373C 100%);
      background-image: -moz-linear-gradient(270deg, #F3575D 0%, #CA373C 100%);
      background-image: -o-linear-gradient(270deg, #F3575D 0%, #CA373C 100%);
      background: linear-gradient(270deg, #F3575D 0%, #CA373C 100%);
    }

    .link{
      color: #CB373D;
    }
  }
  &:first-child{
    margin-top: 0;
  }
  .link{
    width: 0.78rem;
    line-height: 2.25;
    text-align: center;
    background: #ffffff;
    border-radius: @button-radius;
    font-size: @font-tip-size;
    color: #49517A;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 0.16rem;
    z-index: 50;

    .disabled{
      opacity: 0.5;
    }
  }
  .tips{
    padding: 0.08rem 0.12rem;
    min-height: 0.38rem;
    font-size: @font-tip-size;
    line-height: 1.5;
    color: @error-color;
  }
}
.tp_bank_info{
  padding: 0.15rem 0.12rem;
  position: relative;
  border-radius: @module-radius-2;
  color: #ffffff;

  .pic{
    width: 0.42rem;
    height: 0.42rem;
    padding: 0.08rem;
    background: #ffffff;
    border-radius: 50%;
    float: left;

    img{
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .cont{
    margin-left: 0.54rem;
    min-height: 0.42rem;
    font-size: @font-tip-size;
    line-height: 1.33333;
    position: relative;

    h5{
      line-height: 1.375;
      font-size: @font-form-size;
      font-weight: 500;
    }
    .num{
      margin-top: 0.08rem;
      font-size: @font-spel-size-1;
      line-height: 1.2;
    }

    .state{
      font-size: @font-tip-size;
      line-height: 1.8333;
      color: #ffffff;
      position: absolute;
      top: 0;
      right: 0.04rem;
    }
  }
  .tp_tag{
    display: inline-block;
    padding: 0 0.06rem;
    line-height: 0.2rem;
    font-size: @font-tip-size;
    vertical-align: top;
    position: relative;
    font-style: normal;
    color: #49517A;
    top: 0.01rem;
    margin-left: 0.06rem;
    z-index: 5;
    -webkit-transform: scale(0.83);
    transform: scale(0.83);

    &:before{
      content: "";
      width: 100%;
      height: 100%;
      background: rgba(255,255,255,0.7);
      border-radius: 0.03rem;
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      // -webkit-transform: matrix(0.99, 0, -0.16, 1, 0, 0);
      transform: matrix(1, 0, -0.3, 1, 0, 0);
    }

    &.zhu{
      color: #CB373D;
    }
  }
  // .tp_tag{
  // 	display: inline-block;
  // 	width: 0.18rem;
  // 	height: 0.18rem;
  // 	background: url(../images/tp_tag_fu.png) no-repeat center;
  // 	background-size: 100%;
  // 	vertical-align: top;
  // 	position: relative;
  // 	top: 0.02rem;
  // 	margin-left: 0.1rem;

  // 	&.zhu{
  // 		background-image: url(../images/tp_tag_zhu.png);
  // 	}
  // }
}


/*-- 基本信息修改 --*/
.search_box {
  padding: 0.06rem 0.2rem;
}
.search_box + .layer_cont,
.search_box + .layer_cont.fixed{
  height: 4.42rem;
}
.search_input {
  height: 0.32rem;
  position: relative;

  .icon {
    display: block;
    width: 0.2rem;
    height: 0.2rem;
    text-align: center;
    font-size: 0.16rem;
    line-height: 0.2rem;
    color: @t-color-lightgray;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    margin-top: -0.1rem;
    left: 0.1rem;

    &:before{
      content: "\e67e";
    }

  }
  .t1 {
    display: block;
    width: 100%;
    padding: 0.06rem 0.1rem 0.06rem 0.36rem;
    border: 0 none;
    background: @bg-color;
    border-radius: @button-radius;
    outline: none;
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-normal;
  }
  .txt_close {
    background-color: transparent;
    right: 0.1rem;
  }
}
.upload_infobox + .wx_tips{
  margin-top: 0;
}
.check_cmlist{
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;

    &:last-child{
      border-bottom: 0 none;
    }

    .icon_check,
    .icon_radio{
      display: block;
      padding: 0.12rem 0 0.12rem 0.32rem;
      font-size: @font-form-size;
      line-height: 1.5;
    }
  }
}
.tax_info_form{
  .input_text.text .t1,
  .input_text.text .dropdown,
  .input_text.text .tarea1 {
    padding-left: 1.7rem;
  }
}


/*-- 选择账户  --*/
.acct_status_item{
  background: #ffffff;
  margin-bottom: 0.08rem;

  .opacity5 {
    opacity: 0.5;
  }
  .tit{
    margin: 0 0.16rem;
    position: relative;
    font-size: @font-form-size;
    line-height: 1.5;
    font-weight: 500;
    border-bottom: 1px solid @border-color;

    h5{
      padding: 0.16rem 0.6rem 0.16rem 0;
      font-size: @font-sub-title-size;
      line-height: 1.5;
      font-weight: 500;
      color: @t-color-normal;
    }

    .icon_radio,
    .icon_check{
      display: block;
      font-size: @font-form-size;
      line-height: 1.5;
      padding: 0.16rem 0.6rem 0.16rem 0.3rem;
      color: @t-color-normal;

      &.disabled{
        color: @t-color-lightgray;
      }

    }
    .icon_check:before{
      width: 0.18rem;
      height: 0.18rem;
      font-size: 0.16rem;
      margin-top: -0.09rem;
    }
    .state{
      font-size: @font-base-size;
      line-height: 1.71;
      color: @t-color-lightgray;
      position: absolute;
      top: 50%;
      margin-top: -0.12rem;
      right: 0;

      &.error{
        color: @error-color;
      }
    }
  }
}
.acct_status_item + .com_title{
  padding-top: 0.07rem;
}
.acct_list{
  padding: 0 0.16rem;
  background: #ffffff;

  li{
    border-bottom: 1px solid @border-color;
    position: relative;

    &:last-child{
      border-bottom: 0 none;
    }

    p{
      padding: 0.16rem 0;
      font-size: @font-form-size;
      line-height: 1.5;
      color: @t-color-normal;
    }
    .icon_radio,
    .icon_check{
      display: block;
      padding: 0.16rem 0 0.16rem 0.3rem;
      font-size: @font-form-size;
      line-height: 1.5;
      color: @t-color-normal;

    }
    .icon_check:before{
      width: 0.18rem;
      height: 0.18rem;
      font-size: 0.16rem;
      margin-top: -0.09rem;
    }
    .state{
      font-size: @font-base-size;
      line-height: 1.71;
      color: @t-color-lightgray;
      position: absolute;
      top: 0.16rem;
      right: 0;

      &.error{
        color: @error-color;
      }
    }
    .txt_cont{
      padding: 0 0 0.08rem 0.3rem;
      font-size: @font-base-size;
      line-height: 1.42;
      color: @t-color-lightgray;
      position: relative;
      top: -0.08rem;
    }
  }
}
.acct_list_tips {
  font-size: 0.14rem;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
  color: #999999;
  word-break: break-all;
  padding: 0.2rem 0.16rem 0.16rem;
}
.result_bottom_tips {
  font-size: 0.14rem;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
  color: #999999;
  word-break: break-all;
  padding: 0.2rem 0.16rem 0.16rem;
}
.appro_ly_result{
  padding: 0.2rem 0.16rem;
  text-align: center;

  h3{
    font-size: @font-spel-size-1;
    line-height: 1.4;
    font-weight: 500;
  }

  .appro_ic_ok,
  .appro_ic_error{
    width: 0.22rem;
    height: 0.22rem;
    font-size: 0.22rem;
    margin-left: 0;
    margin-right: 0.08rem;
    position: relative;
    top: 0.03rem;
  }
}
.appro_ly_cont{
  height: 4.66rem;
  overflow: auto;

  .appro_tips{
    margin: 0 0.16rem;
    padding: 0.2rem 0;
    color: @t-color-lightgray;
    border-top: 1px solid @border-color;
    border-bottom: 1px solid @border-color;
  }
  .rule_check{
    margin: 0;
  }
}
.appro_ly_cont + .ce_btn{
  padding: 0.06rem 0.16rem;
}
.appro_info.tb_border{
  padding: 0;
  margin: 0 0.16rem;
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
}
.acct_imptips{
  background: #FF9993;
  padding: 0.1rem 0.16rem;
  text-align: center;
  font-size: @font-base-size;
  line-height: 1.42;
  color: #ffffff;
}


/*-- 首页  --*/
.icon_search{
  width: 0.48rem;
  height: 0.44rem;
  line-height: 0.44rem;
  text-align: center;
  font-size: 0.2rem;
  color: #0F1826;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 50;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  visibility: visible;
  opacity: 1;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;

  &:before{
    content: "\e67e";
  }

  &.disabled{
    opacity: 0;
    visibility: hidden;
  }
}
.search_link{
  padding: 0.08rem 0.2rem;
  background: #ffffff;

  a{
    display: block;
    line-height: 2.57;
    padding: 0 0.16rem;
    font-size: @font-base-size;
    color: @t-color-lightgray;
    background: @bg-color;
    border-radius: @button-radius;

    &:before{
      content: "\e8b9";
      display: inline-block;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-size: 0.16rem;
      line-height: 0.16rem;
      color: @t-color-lightgray;
      position: relative;
      vertical-align: top;
      top: 0.1rem;
      margin-right: 0.08rem;
    }

  }
}
.top_searchbox{
  padding: 0.04rem 0.52rem 0.04rem 0;
  margin: 0 0.08rem 0 0.2rem;
  position: relative;

  .icon{
    width: 0.16rem;
    height: 0.16rem;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 0.16rem;
    line-height: 0.16rem;
    color: @t-color-lightgray;
    position: absolute;
    left: 0.16rem;
    top: 0.14rem;
    z-index: 50;

    &:before{
      content: "\e8b9";
    }
  }
  .t1{
    display: block;
    width: 100%;
    border: 0 none;
    background: @bg-color;
    border-radius: @button-radius;
    line-height: 1.42;
    padding: 0.08rem 0.6rem 0.08rem 0.4rem;
    outline: none;
    font-size: @font-base-size;
    font-weight: 500;
    color: @t-color-normal;
    font-family: -apple-system-font, "Helvetica Neue", sans-serif;
  }
  .btn{
    width: 0.52rem;
    text-align: center;
    font-size: @font-base-size;
    line-height: 2.57;
    color: @main-color;
    font-weight: 500;
    position: absolute;
    top: 0.04rem;
    right: 0;
    z-index: 50;
  }
}
.s_keywords{
  font-weight: normal;
  font-style: normal;
  color: @main-color !important;
}
.search_delete{
  display: inline-block;
  width: 0.28rem;
  height: 0.28rem;
  text-align: center;
  position: absolute;
  top: 50%;
  right: 0.12rem;
  margin-top: -0.14rem;

  svg{
    display: inline-block;
    fill: #BBBBBB;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
  }
}
.search_history{
  padding: 0 0.08rem 0 0.2rem;
  font-size: 0;
  margin-bottom: -0.08rem;

  li{
    display: inline-block;
    vertical-align: top;
    margin-right: 0.12rem;
    margin-bottom: 0.08rem;

    span{
      display: inline-block;
      vertical-align: top;
      padding: 0.06rem 0.12rem;
      font-size: @font-base-size;
      line-height: 1.42;
      border-radius: 0.5rem;
      background: @bg-color;
      color: @t-color-normal;
    }
  }
}


.bus_tp_title{
  position: relative;

  .icon{
    display: inline-block;
    width: 0.18rem;
    height: 0.18rem;
    position: relative;
    top: 0.05rem;
    margin-right: 0.08rem;
    display: inline-block;
    vertical-align: top;
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 0.18rem;
    line-height: 1;
    color: @main-color;
    display: none;


    &.hot:before{
      content: "\e629";
    }
    &.open:before{
      content: "\e6b3";
    }
    &.user:before{
      content: "\e62b";
    }
    &.other:before{
      content: "\e62a";
    }
  }


}


/*-- 登录 --*/
.login_page{
  padding: 0.2rem 0;
}
.login_title{
  padding: 0 0.16rem;
  margin-bottom: 0.2rem;

  h3{
    font-size: 0.28rem;
    font-weight: 500;
    line-height: 0.4rem;
  }
  p{
    font-size: @font-base-size;
    line-height: 1.42;
    margin-top: 0.04rem;
    color: @t-color-gray;
  }
}
.login_form{
  padding: 0 0.16rem;
  background: #ffffff;

  .input_text:last-child{
    border-bottom: 1px solid @border-color;
  }
}
.login_otheropea{
  margin: 0.2rem 0.16rem;
  display: flex;
  justify-content: space-between;

  a{
    display: block;
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-gray;
  }
}


.reject_box {
  background: #FFF0F0;
  position: relative;
  color: @error-color;
  z-index: 100;
  width: 100%;
  z-index: 200;

  h5 {
    padding: 0.14rem 0.56rem 0.14rem 0.16rem;
    line-height: 1.42;
    font-size: @font-base-size;
    font-weight: 500;
    position: relative;

    &:after {
      content: "\e619";
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color:@error-color;
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 0.16rem;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: 0.16rem;
    }
    &.on:after {
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg);
    }
    span {
      padding-left: 0.28rem;
      position: relative;

      &:before {
        content: "\e609";
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color:@error-color;
        width: 0.2rem;
        height: 0.2rem;
        font-size: 0.18rem;
        line-height: 0.2rem;
        position: absolute;
        top: 50%;
        margin-top: -0.1rem;
        left: 0;
      }
    }
    &.no_arrow{
      padding-right: 0.16rem;
      &:after{
        display: none;
      }
    }
  }
  .cont {
    width: 100%;
    background: #FFF0F0;
    font-weight: 500;
    padding: 0 0.2rem 0.14rem 0.44rem;
    line-height: 1.42;
    font-size: @font-base-size;

    p {
      padding: 0.02rem 0;
    }
  }
}


/*-- ios 沉浸式处理  --*/
.iosSafeArea{
  .header{
    padding-top:0px;
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }
}


/*--  业务介绍页  --*/
.bus_banbox{
  height: 2.95rem;
  background: url(../images/bus_ban_bg.png) no-repeat center bottom;
  background-size: 100% 100%;
  position: relative;
  margin-bottom: 0.1rem;

  .txt{
    width: 100%;
    padding: 0.4rem 0.15rem 0;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);

    h2{
      font-size: 0.4rem;
      line-height: 0.52rem;
      font-weight: 700;
      background: linear-gradient(180deg, #FFFFFF 57.85%, #FFCDCC 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    p{
      font-size: 0.4rem;
      line-height: 0.52rem;
      font-weight: 700;
      background: linear-gradient(180deg, #FFFFFF 57.85%, #FFCDCC 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .tips{
      text-align: center;
      font-size: @font-base-size;
      line-height: 1.4;
      color: rgba(255,255,255, 0.7);
      margin-top: 0.15rem;
    }
  }
}
.bus_infobox{
  padding: 0.1rem 0.16rem 0.15rem;

  .title{
    padding-left: 0.16rem;
    position: relative;
    font-size: @font-sub-title-size;
    font-weight: 500;
    line-height: 1.375;
    color: @t-color-normal;

    i{
      display: block;
      width: 0.1rem;
      height: 0.1rem;
      padding: 0.02rem;
      background: @main-color-10;
      border-radius: 50%;
      position: absolute;
      top: 0.06rem;
      left: 0;

      &:before{
        content: "";
        display: block;
        width: 100%;
        height: 100%;
        background: @main-color;
        border-radius: 50%;
      }
    }
  }
  .cont{
    padding-left: 0.16rem;
    font-size: @font-base-size;
    line-height: 1.57;
    color: @t-color-gray;
    margin-top: 0.08rem;

    h5{
      font-size: @font-base-size;
      line-height: 1.6;
      font-weight: 500;
      color: @t-color-normal;
      margin: 0.08rem 0 0.04rem;
    }

    .tips{
      color: @t-color-lightgray;
      margin-top: 0.04rem;
    }
  }
}
.wx_cm_tips{
  text-align: center;
  font-size: @font-form-size;
  line-height: 1.375;
  color: @t-color-gray;
  margin: 0.3rem 0.16rem 0.2rem;
}
.appro_page{
  background: #ffffff;

  .appro_tips{
    margin: 0 0.16rem;
    border-top: 1px solid @border-color;
    color: @t-color-lightgray;
    padding: 0.2rem 0 0;
    margin-top: 0.2rem;
  }

}


/*-- 知识测评 --*/
.zs_test_result{
  padding: 0.2rem 0.3rem 0.3rem;
  text-align: center;

  h3{
    font-size: @font-spel-size-2;
    line-height: 1.333;
    font-weight: 500;
  }
  p{
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-gray;
    margin-top: 0.12rem;
  }
}

.zs_test_level {
  width: 2.2rem;
  height: 2.2rem;
  margin: 0 auto -0.16rem;
  position: relative;

  .txt {
    width: 100%;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-35%);
    transform: translateY(-35%);
    left: 0;
    z-index: 50;

    h5 {
      height: 0.72rem;
      line-height: 0.72rem;
      font-size: @font-form-size;
      font-weight: normal;
      color: @suc-color-2;

      &.error{
        color: @error-color;
      }

      strong {
        font-size: 0.52rem;
        margin-right: 0.04rem;
        font-weight: 700;
      }
    }
    p {
      font-size: @font-form-size;
      color: @t-color-lightgray;
      line-height: 1.375;
      margin-top: 0;
    }
  }
}
.zs_test_canvas canvas {
  width: 2.2rem;
  height: 2.2rem;
  margin: 0 auto;
  display: block;
}


/*-- 问卷回访 --*/

.visit_box {
  background: #ffffff;
  margin: 0.08rem 0;

  h5 {
    padding: 0.12rem 0;
    margin: 0 0.16rem;
    font-size: @font-sub-title-size;
    line-height: 1.5;
    font-weight: normal;
    position: relative;

    .num {
      font-style: normal;
      position: absolute;
      top: 0.12rem;
      left: 0;
    }
  }
  ul {
    border-top: 1px solid @border-color;
    display: flex;

    li {
      flex: 1;
      position: relative;
      padding: 0.16rem;
      text-align: center;
      line-height: 1.5;
      font-size: @font-form-size;
      color: @t-color-gray;
      border-left: 1px solid @border-color;

      &:first-child {
        border-left: 0 none;
      }
      span {
        position: relative;
        font-size: @font-form-size;
      }
      &.checked{
        span {
          color: @main-color;

          &:before {
            content: "\e61c";
            width: 0.24rem;
            height: 0.24rem;
            text-align: center;
            font-size: 0.24rem;
            line-height: 1;
            color: @main-color;
            font-family: "wt-iconfont" !important;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: absolute;
            top: 50%;
            margin-top: -0.12rem;
            right: -0.28rem;
          }
        }
      }
    }
  }
}
.select_levellist{
  padding: 0 0.16rem;
  li{
    border-bottom: 1px solid @border-color;
    padding: 0.12rem 0;
    background: #ffffff;

    &:first-child{
      margin-top: 0;
    }
    .tit{
      display: flex;
      font-size: @font-base-size;
      line-height: 1.57;

      h5{
        flex: 1;
        width: 100%;
        font-size: @font-sub-title-size;
        line-height: 1.5;
        font-weight: normal;
      }
      .icon_check,
      .icon_radio{
        display: block;
        padding: 0;
        width: 0.2rem;
        height: 0.2rem;
        position: relative;
        top: 0.02rem;
        margin-left: 0.15rem;
      }
      .icon_check:before{
        width: 0.18rem;
        height: 0.18rem;
        font-size: 0.16rem;
        margin-left: 0.01rem;
      }
      .state{
        display: block;
        color: @t-color-lightgray;
        margin-left: 0.15rem;

        &.error{
          color: @error-color;
        }
      }
    }
    .cont{
      margin-top: 0.06rem;
      font-size: @font-base-size;
      line-height: 1.85;
      color: @t-color-lightgray;

      p{
        position: relative;
        padding-left: 0.14rem;

        &:before{
          content: "";
          width: 0.04rem;
          height: 0.04rem;
          border-radius: 50%;
          background: @t-color-lightgray;
          position: absolute;
          top: 0.11rem;
          left: 0;
        }
      }
    }
  }
}
.wtclass_main{
  padding: 0.08rem 0;
}
.wtclass_list{
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    display: flex;
    align-items: center;
    padding: 0.16rem 0;
    font-size: @font-form-size;
    line-height: 1.5;

    &:last-child{
      border-bottom: 0 none;
    }
    p{
      flex: 1;
      width: 100%;
    }

    .switch{
      display: block;
      margin-left: 0.15rem;
    }

  }
}
.sub_protocol_list{
  text-align: center;

  a{
    display: block;
    font-size: @font-form-size;
    line-height: 1.375;
    color: @link-color;
  }
}
.check_all{
  display: block;
  padding-right: 0.24rem;
  font-size: @font-base-size;
  color: @t-color-gray;
  margin-left: 0.15rem;

  &:before{
    left: auto;
    right: 0;
    width: 0.18rem;
    height: 0.18rem;
    font-size: 0.16rem;
    margin-top: -0.09rem;
  }
  &.not_all{
    &:before{
      background-color: @disbaled-bg-50;
    }
    &:after{
      content: '';
      width: 0.08rem;
      height: 0.08rem;
      border-radius: 0.02rem;
      background: @main-color;
      position: absolute;
      top: 50%;
      margin-top: -0.04rem;
      right: 0.05rem;
      z-index: 10;
    }
  }

}
.fund_acct_scroll{
  max-height: calc(100% - 0.98rem);
  overflow: auto;
}
.fund_acct_item{
  .letter{
    padding: 0.05rem 0.16rem;
    font-size: @font-base-size;
    line-height: 1.3;
    font-weight: 500;
    color: @t-color-normal;
  }
  .acct_list li{
    .icon_radio,
    .icon_check {
      padding: 0.16rem 0.3rem 0.16rem 0;

      &:before{
        left: auto;
        right: 0;
      }
      &.disabled:before{
        display: none;
      }
    }
  }
}
.imp_span{
  color: @imp_color;
}
.zj_account_card{
  margin: 0.08rem 0.2rem 0.4rem;
  background: linear-gradient(251.71deg, #E9BA8F -12.41%, #CB996B 100%);
  position: relative;
  border-radius: 0.12rem;
  z-index: 10;

  &:before{
    content: "";
    height: 0.4rem;
    width: 100%;
    background: linear-gradient(180deg, rgba(184, 137, 110, 0.7) 0%, rgba(231, 185, 141, 0) 61.9%);
    filter: blur(0.05rem);
    border-radius: 0.18rem;
    position: absolute;
    bottom: -0.34rem;
    left: 0;
    z-index: -1;
  }
  .cont{
    padding: 0.24rem;
    min-height: 1.9rem;
    background: url(../images/zh_bg.png) no-repeat center;
    background-size: cover;
    border-radius: 0.12rem;
    position: relative;
    z-index: 10;
    color: #ffffff;

    .logo{
      margin-bottom: 0.34rem;

      img{
        display: block;
        height: 0.21rem;
      }
    }
    .name{
      font-size: @font-tip-size;
      line-height: 0.21rem;
      position: absolute;
      top: 0.24rem;
      right: 0.24rem;
    }

    p{
      font-size: @font-tip-size;
      line-height: 1.3333;
      color: #ffffff;
      opacity: 0.6;
    }
    .num{
      font-size: 0.3rem;
      line-height: 0.38rem;
      font-weight: 500;
      color: #ffffff;
      margin: 0.02rem 0;
    }
    .copy_btn{
      display: inline-block;
      padding: 0 0.11rem;
      font-size: 0.13rem;
      color: #ffffff;
      background: #EBBD91;
      border-radius: 0.5rem;
      margin-left: 0.14rem;
      line-height: 1.84;
      vertical-align: middle;
    }
    .time{
      margin-top: 0.13rem;
      font-size: @font-tip-size;
      line-height: 1.3333;
      color: #ffffff;
      opacity: 0.6;
    }
    .btn{
      text-align: center;
      margin-top: 0.24rem;

      a{
        display: inline-block;
        vertical-align: top;
        padding: 0 0.11rem;
        font-size: @font-base-size;
        line-height: 1.85;
        border: 1px solid @button-bg-1;
        color: @button-bg-1;
        border-radius: @button-radius;
      }
    }

  }
}
.zj_account_card + .wx_tips{
  margin-left: 0.2rem;
  margin-right: 0.2rem;
}
.tab_nav_wrap{
  background: #ffffff;
  height: 0.48rem;
  overflow: hidden;
}
.tab_navlist{
  height: 0.68rem;
  overflow: auto;
  display: flex;
  padding: 0 0.08rem;

  li{
    flex: 1;

    span{
      display: block;
      padding: 0 0.08rem;
      text-align: center;
      white-space: nowrap;
      height: 0.48rem;
      line-height: 0.48rem;
      font-size: @font-base-size;
      color: @t-color-normal;
      position: relative;

      &:after{
        content: "";
        width: 0.32rem;
        height: 0.03rem;
        background: @main-color;
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -0.16rem;
        border-radius: 0.03rem;
        opacity: 0;
        visibility: hidden;
      }
    }
    &.active{
      span{
        font-weight: 500;

        &:after{
          opacity: 1;
          visibility: visible;
        }
      }
    }
  }
}
.bus_ratelist{
  margin: 0.08rem 0;
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    padding: 0.12rem 0;
    display: flex;
    align-items: center;

    &:last-child{
      border-bottom: 0 none;
    }
    .row_1{
      flex: 1;
      width: 100%;
    }
    h5{
      font-size: @font-sub-title-size;
      line-height: 1.5;
      font-weight: normal;
    }
    p{
      font-size: @font-base-size;
      line-height: 1.57;
      color: @t-color-lightgray;
      margin-top: 0.02rem;
    }
    .state{
      display: block;
      font-size: @font-base-size;
      line-height: 1.57;
      color: @t-color-lightgray;
      margin-left: 0.15rem;

      &.error{
        color: @error-color;
      }
    }
  }
  .r_link_arrow{
    display: block;
    margin-left: 0.15rem;
  }
}
.r_link_arrow{
  display: inline-block;
  vertical-align: top;
  font-size: @font-base-size;
  line-height: 1.57;
  padding-right: 0.2rem;
  position: relative;
  color: @link-color;

  &:after{
    content: "\e619";
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: @link-color;
    width: 0.16rem;
    height: 0.16rem;
    font-size: 0.16rem;
    line-height: 0.16rem;
    position: absolute;
    top: 50%;
    margin-top: -0.08rem;
    right: 0;
  }
}

.com_title{
  h5 .small{
    font-size: @font-tip-size;
    color: @t-color-lightgray;
    margin-left: 0.1rem;
  }
  .switch{
    margin-top: -0.03rem;
  }
}
.tab_nav{
  border-top: 1px solid @border-color;
  background: #ffffff;
  margin-bottom: 0.08rem;
  box-shadow: 0 0.02rem 0.08rem rgba(0,0,0,0.02);

  ul{
    display: flex;

    li{
      flex: 1;

      span{
        display: block;
        position: relative;
        padding: 0.12rem 0;
        text-align: center;
        font-size: @font-form-size;
        line-height: 1.5;
        color: @t-color-gray;
      }
      &.active{
        span{
          color: @main-color;

          &:after{
            content: "";
            width: 0.32rem;
            height: 0.03rem;
            border-radius: 0.03rem;
            position: absolute;
            bottom: 0;
            left: 50%;
            margin-left: -0.16rem;
          }
        }
      }
    }
  }
}
.visit_navlist{
  background: #ffffff;
  padding: 0 0.16rem;
  margin-bottom: 0.1rem;

  li{
    border-bottom: 1px solid @border-color;
    display: flex;
    padding: 0.12rem 0;
    font-size: 0.16rem;
    line-height: 1.5;
    align-items: center;

    &:last-child{
      border-bottom: 0 none;
    }
    p{
      flex: 1;
      width: 100%;
    }
    .arrow{
      display: block;
      width: 0.16rem;
      height: 0.16rem;

      &:before{
        display: block;
        content: "\e619";
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: @t-color-lightgray;
        width: 0.16rem;
        height: 0.16rem;
        font-size: 0.16rem;
        line-height: 0.16rem;
      }
    }
  }
}

.pro_appro_wrap{
  margin: 0 -0.16rem;
  padding-bottom: 0.15rem;
  border-bottom: 0.08rem solid @bg-color;
  font-size: @font-base-size;
  line-height: 1.4;
  color: @t-color-lightgray;

  .com_title{
    color: @t-color-normal;
  }
  .com_imp_tips{
    padding: 0 0.16rem;
    margin: 0.15rem 0 0;
  }
  > p{
    padding: 0 0.16rem;
  }
}
.pro_appro_item{
  margin: 0 0.16rem;

  .title{
    padding: 0.12rem 0.4rem 0.12rem 0;
    font-size: @font-form-size;
    line-height: 1.4;
    color: @t-color-normal;
    font-weight: 500;
    position: relative;

    &:after{
      content: "\e619";
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: @t-color-lightgray;
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 0.16rem;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: 0.15rem;
    }
    &.on:after{
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg);
    }
  }
}
.pro_appro_tbbox{
  padding: 0;
}
.pro_appro_table {
  width: 100%;
  border: 1px solid @border-color;
  border-radius: 0.04rem;

  th{
    font-size: @font-base-size;
    line-height: 2;
    padding: 0.08rem 0.05rem;
    font-weight: normal;
    text-align: left;
    color: @t-color-gray;
    background: @bg-color;
    border-bottom: 1px solid @border-color;

    &:last-child{
      text-align: center;
      padding-right: 0.15rem;
    }
    &:first-child{
      padding-left: 0.15rem;
    }
  }
  td{
    text-align: left;
    font-size: @font-base-size;
    line-height: 1.4;
    padding: 0.08rem 0.05rem;
    color: @t-color-normal;

    &:last-child{
      text-align: center;
      padding-right: 0.15rem;
    }
    &:first-child{
      padding-left: 0.15rem;
    }
  }
}
.com_imp_tips{
  margin: 0.15rem 0;
  font-size: @font-tip-size;
  line-height: 1.4;
  color: @error-color;
}
.bus_type_wrap{
  background: #ffffff;
  overflow: hidden;
  border-radius: 0.16rem 0.16rem 0 0;
  padding: 0.16rem 0 0;
}
.bus_type_nav{
  padding: 0 0.12rem 0.2rem 0.12rem;
  overflow: auto;
  margin-bottom: -0.2rem;
  display: flex;

  li{
    margin-left: 0.08rem;

    span{
      display: block;
      height: 0.32rem;
      line-height: 0.32rem;
      text-align: center;
      padding: 0 0.12rem;
      font-size: @font-base-size;
      color: @t-color-normal;
      border-radius: 0.5rem;
      white-space: nowrap;
      background: #ffffff;
      background: @bg-color;
    }

    &.active{
      span{
        background: @button-assist-bg;
        color: @main-color;
      }
    }
  }
}
.bus_type_fixed{
  display: block;
  width: 100%;
  position: fixed;
  top: 0.44rem;
  left: 0;
  z-index: 200;

  &.disabled{
    display: none;
  }
  .bus_type_wrap{
    padding: 0.08rem 0;
    border-radius: 0;
  }

}
.ready_box {
  padding: 0.1rem 0.24rem 0.2rem;

  .title {
    text-align: center;
    line-height: 1.5;
    font-size: @font-form-size;
    font-weight: normal;
    margin-bottom: 0.14rem;
    display: flex;
    align-items: center;
    color: @t-color-lightgray;

    span{
      display: block;
      margin: 0 0.12rem;
    }
    &:before,
    &:after{
      content: "";
      display: block;
      height: 1px;
      flex: 1;
      width: 100%;

    }
    &:before{
      background: linear-gradient(270deg, #E5E5E5 -0.96%, rgba(229, 229, 229, 0) 100%);
    }
    &:after{
      background: linear-gradient(90deg, #E5E5E5 -0.96%, rgba(229, 229, 229, 0) 100%);
    }
  }
  .list{

    li {
      padding-left: 0.56rem;
      margin-top: 0.28rem;
      position: relative;

      &:first-child{
        margin-top: 0 !important;
      }
      i {
        display: block;
        width: 0.4rem;
        height: 0.4rem;
        position: absolute;
        top: 50%;
        margin-top: -0.2rem;
        left: 0;

        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      h5 {
        font-size: @font-form-size;
        font-weight: 500;
        line-height: 1.5;
      }
      p {
        font-size: @font-base-size;
        line-height: 1.4;
        margin-top: 0.04rem;
        color: @t-color-lightgray;
      }
    }
  }
}
.appro_detail_info{
  margin: 0 0.16rem;
  font-size: @font-base-size;
  line-height: 1.4;
  color: @t-color-lightgray;

  dl{
    margin-top: 0.16rem;
    font-size: @font-base-size;
    line-height: 1.4;

    dt{
      margin-bottom: 0.04rem;
      color: @t-color-lightgray;
    }
    dd{
      color: @t-color-normal;

      strong{
        text-decoration: underline;
      }
    }
  }
}
.appro_span_ok{
  color: @suc-color-2;
}
.appro_span_error{
  color: @error-color;
}

.tip_txtbox{
  background: #ffffff;
  margin: 0.08rem 0;
  padding: 0.16rem;
  color: @t-color-lightgray;

  p{
    margin-top: 0.08rem;

    &:first-child{
      margin-top: 0;
    }
  }
}

.white_bg{
  .tip_txtbox{
    margin: -1px 0.16rem 0;
    border-top: 1px solid @border-color;
    padding: 0.16rem 0;
  }
}
.acct_add_tit{
  padding: 0.16rem;
  display: flex;
  font-size: @font-form-size;
  line-height: 1.5;

  p{
    flex: 1;
    width: 100%;
    font-weight: 500;
  }
  .opea{
    font-size: 0;

    .icon_radio{
      margin-left: 0.25rem;
      font-size: @font-form-size;
      line-height: 1.5;


      &:first-child{
        margin-left: 0 !important;
      }
    }
  }
}
.acct_add_cont{
  margin: 0 0.16rem;
  border-top: 1px solid @border-color;
}
.acct_status_item .imp_c_tips{
  margin: 0 0.16rem;
  border-top: 1px solid @border-color;
}
.com_infolist{
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    display: flex;
    border-bottom: 1px solid @border-color;
    padding: 0.16rem 0;
    font-size: @font-form-size;
    line-height: 1.5;

    .tit{
      display: block;
      min-width: 1rem;
      color: @t-color-gray;
      margin-right: 0.16rem;
    }
    p{
      flex: 1;
      width: 100%;
      text-align: right;
    }
  }
}
.cm_pwordbox{
  min-height: 1.28rem;
  padding: 0.16rem 0.3rem;

  .error_tips{
    top: 0;
    margin-top: 0.12rem;
    font-size: @font-base-size;
  }
}
.cm_pword_input{
  display: flex;
  justify-content:space-between;

  .item{
    width: 0.42rem;
    height: 0.42rem;
    background: #F1F1F5;
    border-radius: 0.04rem;
    position: relative;

    &.off{
      &:before{
        content: "";
        width: 0.1rem;
        height: 0.1rem;
        background: @t-color-gray;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -0.05rem 0 0 -0.05rem;
      }
    }
  }
  &.error{
    .item{
      background: @error-color-2;
    }
  }
}
.acct_s_tag{
  display: inline-block;
  padding: 0 0.03rem;
  border-radius: 0.02rem;
  border: 1px solid @suc-color-2;
  color: @suc-color-2;
  font-size: @font-tip-size;
  line-height: 1.33333;
  vertical-align: middle;
  margin-left: 0.08rem;

  &.abnormal{
    border-color: @border-deep-color;
    color: @border-deep-color;
  }
}
.acct_emptybox{
  background: #ffffff;
  padding: 0.16rem;
  display: flex;
  align-items: center;
  font-size: @font-form-size;
  line-height: 1.5;

  p{
    flex: auto;
  }
}
.state_ic_ok,
.state_ic_error{
  display: inline-block;
  width: 0.22rem;
  height: 0.22rem;
  font-size: 0.22rem;
  line-height: 1;
  text-align: center;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.state_ic_ok:before {
  content: "\e651";
  color: @suc-color;
}
.state_ic_error:before {
  content: "\e609";
  color: @error-color;
}
.acct_cond_list{
  background: #ffffff;
  padding: 0 0.16rem;

  li{
    padding: 0.16rem 0;
    font-size: @font-form-size;
    line-height: 1.5;
    position: relative;
    border-bottom: 1px solid @border-color;

    &:last-child{
      border-bottom: 0 none;
    }

    .erro_cont{
      margin-top: 0.08rem;
      font-size: @font-base-size;
      line-height: 1.5;
      color: @error-color;

      p{
        padding-left: 0.12rem;
        position: relative;

        &:before{
          content: '';
          width: 0.04rem;
          height: 0.04rem;
          background: @error-color;
          border-radius: 50%;
          position: absolute;
          top: 0.08rem;
          left: 0;
        }
      }
    }
    .state_ic_ok,
    .state_ic_error{
      position: absolute;
      top: 0.17rem;
      right: 0;
    }
  }
}
.error_color{
  color: @error-color !important;
}
.acct_list li{
  .input_form{
    padding: 0 0 0 0.3rem;
  }
  .input_text .tit{
    position: absolute;
    margin: 0;
  }
}

/*-- add 20230426 --*/
.drop_arrow{
  display: inline-block;
  width: 0.16rem;
  height: 0.16rem;

  &:before{
    content: "\e619";
    font-family: "wt-iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: @border-deep-color;
    width: 0.16rem;
    height: 0.16rem;
    font-size: 0.16rem;
    line-height: 0.16rem;
    position: absolute;
    top: 50%;
    margin-top: -0.08rem;
    right: 0;
  }
  &.on{
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}
.acct_status_item .tit{

  .drop_arrow{
    position: absolute;
    top: 50%;
    margin-top: -0.08rem;
    right: 0;
  }
  h5 .state_ic_error{
    vertical-align: middle;
    margin-left: 0.08rem;
  }
}
.acct_list li{
  .txt_p{
    font-size: @font-form-size;
    line-height: 1.5;
    padding: 0.16rem 0;
  }
  .erro_cont {
    padding-bottom: 0.16rem;
    font-size: @font-base-size;
    line-height: 1.5;
    color: @error-color;

    .item {
      padding-left: 0.12rem;
      position: relative;

      &:before {
        content: '';
        width: 0.04rem;
        height: 0.04rem;
        background: @error-color;
        border-radius: 50%;
        position: absolute;
        top: 0.08rem;
        left: 0;
      }
    }
  }
}


/*-- add 20230505 --*/
.yy_must_list{
  padding: 0.2rem 0.16rem;

  li{
    margin-top: 0.2rem;
    border: 1px solid @border-color;
    padding: 0.28rem 0.2rem;
    display: flex;
    border-radius: 0.04rem;

    &:first-child{
      margin-top: 0;
    }

    .icon{
      display: block;
      width: 0.48rem;
      height: 0.48rem;
      margin-right: 0.2rem;

      img{
        display: block;
        width: 100%;
      }
    }
    .cont{
      flex: 1;
      width: 100%;

      h5{
        font-size: 0.22rem;
        line-height: 0.3rem;
        font-weight: normal;
        color: @t-color-normal;
        margin-bottom: 0.04rem;
      }

      p{
        font-size: 0.13rem;
        line-height: 0.18rem;
        color: @t-color-lightgray;
        margin-top: 0.04rem;

        &.tips{
          color: @error-color;
          margin-top: 0.08rem;
        }
      }


    }
    &.error{
      border-color: @error-color;

      h5{
        &:after{
          content: "\e609";
          color: @error-color;
          display: inline-block;
          width: 0.24rem;
          height: 0.24rem;
          font-size: 0.22rem;
          line-height: 0.24rem;
          text-align: center;
          font-family: "wt-iconfont" !important;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          vertical-align: top;
          margin-left: 0.1rem;
          position: relative;
          top: 0.03rem;
        }
      }

    }
    &.ok{
      background: #FCFCFC;

      h5{
        &:after{
          content: "\e651";
          color: @suc-color;
          display: inline-block;
          width: 0.24rem;
          height: 0.24rem;
          font-size: 0.22rem;
          line-height: 0.24rem;
          text-align: center;
          font-family: "wt-iconfont" !important;
          font-style: normal;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          vertical-align: top;
          margin-left: 0.1rem;
          position: relative;
          top: 0.03rem;
        }
      }

    }
  }
}

.spel_info_supp{
  .mid_title{
    margin: 0.09rem 0;
    padding: 0 0.16rem;
    background: none;
    font-size: @font-tip-size;
    line-height: 1.3;
    display: flex;
    align-items: center;

    p{
      flex: 1;
      width: 100%;
    }
    a{
      margin-left: 0.15rem;
    }

    &:after{
      display: none;
    }
  }
  .input_text.text {
    .t1,
    .dropdown,
    .tarea1 {
      padding-left: 1.08rem;
      text-align: right;
    }
  }
  .com_box{
    margin-bottom: 0.08rem;
  }
}
.tj_videolist{
  padding: 0.16rem;

  li{
    margin-top: 0.08rem;
    border: 1px solid @border-color;
    border-radius: @module-radius;
    padding: 0.19rem 0.15rem;
    display: flex;

    &:first-child{
      margin-top: 0;
    }

    .pic{
      width: 0.84rem;
      height: 0.84rem;
      margin-right: 0.16rem;
      position: relative;
      overflow: hidden;
      border-radius: @module-radius;

      img{
        display: block;
        width: 100%;
      }
    }
    .cont{
      flex: 1;
      width: 100%;

      h5{
        font-size: @font-form-size;
        line-height: 1.4;
        font-weight: normal;
        margin-bottom: 0.04rem;
        color: @t-color-normal;
      }
      p{
        font-size: 0.13rem;
        line-height: 0.2rem;
        color: @t-color-lightgray;
      }
    }
    .info{
      display: flex;
      margin-top: 0.06rem;
      align-items: center;
      font-size: 0.13rem;
      line-height: 0.2rem;
      color: @t-color-lightgray;

      .row_01{
        flex: 1;
        width: 100%;
      }
    }
    .btn{
      display: block;
      width: 0.8rem;
      font-size: @font-tip-size;
      line-height: 2;
      border-radius: 0.5rem;
      background: @button-bg-1;
      color: #ffffff;
      text-align: center;
      margin-left: 0.1rem;
    }
    .state{
      margin-right: 0.1rem;

      &.ok{
        color: @suc-color;
      }
      &.error{
        color: @error-color;
      }
    }
  }
}
.play_btn{
  width: 100%;
  height: 100%;
  border-radius: @module-radius;
  background: rgba(0,0,0,0.1);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;

  &:before{
    content: "";
    width: 0.32rem;
    height: 0.32rem;
    background: url(../images/play_btn.png) no-repeat center;
    background-size: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.16rem 0 0 -0.16rem;
  }
}
.bottom_tips{
  font-size: @font-tip-size;
  line-height: 1.42;
  color: @t-color-lightgray;
  padding: 0 0.16rem 0.16rem;
}
.white_bg{
  .com_tips{
    margin: 0 0.16rem;
    border-top: 1px solid @border-color;
    padding-left: 0;
    padding-right: 0;
  }
}
.date_input{
  display: flex;
  align-items: center;

  .item{
    flex: 1;
    width: 100%;
    height: 0.56rem;
    line-height: 0.56rem;
    font-size: 0.16rem;

    .t1{
      padding-left: 0 !important;
      padding-right: 0 !important;

    }
  }
  .line{
    margin: 0 0.05rem;
  }
}
.input_text.text{
  .date_input{
    padding-left: 0.88rem;
  }
}
.nav_sele_wrap{
  .com_title{
    background: #ffffff;
  }
}
.nav_selelist {
  margin: 0.08rem 0.16rem 0.2rem;

  .item {
    background: #fff;
    border-radius: 0.04rem;
    box-shadow: 0px 0.04rem 0.15rem 0px rgba(9, 31, 60, 0.08);
    margin-top: 0.2rem;
    padding: 0.24rem 0.2rem;
    position: relative;

    &:first-child {
      margin-top: 0;
    }
    h5 {
      font-size: 0.2rem;
      line-height: 1.3;
      padding-right: 0.3rem;
      margin-bottom: 0.1rem;
      font-weight: normal;
      color: @t-color-normal;
      position: relative;

      &:after {
        content: "\e619";
        font-family: "wt-iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: @border-deep-color;
        width: 0.16rem;
        height: 0.16rem;
        font-size: 0.16rem;
        line-height: 0.16rem;
        position: absolute;
        top: 50%;
        margin-top: -0.08rem;
        right: 0;
      }
    }
    p {
      font-size: @font-base-size;
      line-height: 1.42;
      color: @t-color-lightgray;
    }
    .imp{
      color: @tips-color !important;
    }
  }
}
.upload_com_title{
  padding: 0.16rem 0.16rem 0;

  p{
    font-size: 0.14rem;
    line-height: 1.42;
    color: @tips-color;
  }
}


.file_emp_layer{
  background: #fff;
  padding: 0.6rem 0;
  width: 100%;
  position: absolute;
  top: 0.44rem;
  bottom: 0;
  left: 0;
  z-index: 500;

  h2{
    text-align: center;
    font-size: 0.24rem;
    font-weight: normal;
    color: @t-color-normal;
    line-height: 1;
    padding-bottom: 0.24rem;
    border-bottom: 1px solid @border-color;
    margin: 0 0.4rem 0.3rem;

    em{
      display: inline-block;
      vertical-align: top;
      text-align: left;
      margin-top: 0.16rem;
      font-size: @font-base-size;
      line-height: 1.42;
      color: @tips-color;
      font-style: normal;
    }
  }
  .pic{
    margin: 0 0.4rem;

    img{
      display: block;
      width: 100%;
    }
  }
  .close{
    width: 0.32rem;
    height: 0.32rem;
    background: url(../images/icon_close3.png) no-repeat center;
    background-size: 100%;
    position: absolute;
    bottom: 9%;
    left: 50%;
    margin-left: -0.16rem;
    z-index: 50;
  }
}
.lz_basebox {
  background: #fff;
  padding: 0.2rem 0.25rem 0.1rem;
  text-align: center;
  font-size: @font-form-size;
  line-height: 1.625;

  h5 {
    font-size: 0.22rem;
    line-height: 1.45;
    font-weight: 500;
    color: @main-color;
    margin-top: 0.1rem;
    text-align: center;
  }
  .pic {
    margin-top: 0.28rem;

    img {
      display: block;
      margin: 0 auto;
      height: 2.5rem;
      max-width: 100%;
    }
  }
}
.v_com_btn {
  padding: 0 0.2rem;
  text-align: center;

  a {
    display: inline-block;
    text-align: center;
    vertical-align: top;
    padding: 0 0.3rem;
    min-width: 1.75rem;
    height: 0.44rem;
    line-height: 0.42rem;
    border: 1px solid @button-bg-1;
    background: @button-bg-1;
    border-radius: 0.5rem;
    font-size: @font-form-size;
    color: #ffffff;

    .begin_icon {
      display: inline-block;
      width: 0.2rem;
      height: 0.2rem;
      border-radius: 50%;
      border: 1px solid #ffffff;
      vertical-align: top;
      margin-right: 0.08rem;
      position: relative;
      top: 0.11rem;

      &:before{
        content: '';
        width: 0.14rem;
        height: 0.14rem;
        border-radius: 50%;
        background: #ffffff;
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -0.07rem 0 0 -0.07rem;
      }
    }
    &.finish {
      border: 1px solid @button-bg-1;
      background: @button-bg-1;
      color: #ffffff;
    }
    &.disabled {
      border-color: @disbaled-bg !important;
      background: none !important;
      color: @disbaled-bg !important;
      opacity: 0.5;

      .begin_icon{
        border-color: @disbaled-bg;

        &:before{
          background: @disbaled-bg;
        }
      }
    }
  }
}
.bc_wrapbox {
  text-align: center;
  background: rgba(48, 48, 48, 0.502826);
  border: 1px solid rgba(122, 122, 122, 0.396939);
  border-radius: 0.08rem;
  padding: 0.1rem 0.15rem 0.12rem;

  h5 {
    font-size: @font-form-size;
    line-height: 1.375;
    font-weight: normal;
    margin-bottom: 0.1rem;
    color: rgba(255, 255, 255, 0.7);

    .imp {
      display: inline-block;
      width: 0.18rem;
      height: 0.18rem;
      text-align: center;
      line-height: 0.18rem;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: rgba(255, 255, 255, 0.7);
      margin-right: 0.06rem;
      position: relative;
      top: 0.02rem;

      &:before{
        content: "\e654";
      }
    }
  }
}
.bc_text {
  text-align: center;

  p {
    text-align: left;
    border-radius: 0.1rem;
    color: #ffffff;
    font-size: 0.18rem;
    line-height: 1.4;

    .readed {
      color: #FFB415;
    }
  }
}
.upload_progress{
  background: #ffffff;
  padding: 0.8rem 0.15rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;
  text-align: center;

  h5 {
    font-size: 0.24rem;
    line-height: 1.2;
    font-weight: normal;
    color: @t-color-normal;
    margin-bottom: 0.1rem;
  }
}
.progress_chart{
  width: 1.93rem;
  height: 1.93rem;
  margin: 0 auto 0.75rem;
  background: url(../images/upload_img1.png) no-repeat center;
  background-size: 100%;
  position: relative;

  &:before{
    display: none;
  }
  .bg{
    content: "";
    width: 100%;
    height: 100%;
    -webkit-animation: allrotate 1s infinite linear;
    animation: allrotate 1s infinite linear;
    position: absolute;
    top: 0;
    left: 0;

    svg{
      display: block;
      width: 100%;
      height: 100%;
      fill: #D69850;
      position: relative;
      z-index: 5;
    }
  }
  .img{
    width: 1.2rem;
    height: 1.2rem;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.6rem 0 0 -0.6rem;
    background: url(../images/video_notimg.png) no-repeat center;
    background-size: 100%;
  }
}
.icon_imp{
  display: inline-block;
  width: 0.16rem;
  height: 0.16rem;
  font-size: 0.16rem;
  line-height: 1;
  text-align: center;
  color: @link-color;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &:before{
    content: "\e654";
  }
}
.file_upload_list{
  font-size: 0;
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.08rem;
  margin-top: -0.08rem;

  li{
    width: 1.09rem;
    height: 1.09rem;
    margin: 0.08rem 0.08rem 0 0;
    position: relative;

    .add{
      display: block;
      height: 1.09rem;
      background: @button-assist-bg;
      border-radius: 0.04rem;
      position: relative;
      text-align: center;
      color: @button-bg-1;
      font-size: @font-tip-size;
      line-height: 1.33333;

      span{
        display: block;
        width: 100%;
        padding-top: 0.36rem;
        position: absolute;
        top: 50%;
        left: 0;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);

        &:before{
          content: '';
          width: 0.24rem;
          height: 0.02rem;
          background: @button-bg-1;
          border-radius: 0.02rem;
          position: absolute;
          top: 0.11rem;
          left: 50%;
          margin-left: -0.12rem;
        }
        &:after{
          content: '';
          height: 0.24rem;
          width: 0.02rem;
          background: @button-bg-1;
          border-radius: 0.02rem;
          position: absolute;
          top: 0;
          left: 50%;
          margin-left: -0.01rem;
        }
      }
    }
    .pic{
      position: relative;
      height: 1.09rem;
      border-radius: 0.04rem;

      img{
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 0.04rem;
      }

    }
    .delete{
      display: block;
      width: 0.18rem;
      height: 0.18rem;
      line-height: 0.18rem;
      text-align: center;
      font-size: 0.14rem;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 0 0.04rem 0 0.04rem;
      color: #ffffff;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 50;

      &:before{
        content: "\e61d";
      }
    }
  }
}
.file_box_01{
  .item{
    background: #ffffff;
    margin-top: 0.08rem;
    padding: 0 0.16rem;

    &:first-child{
      margin-top: 0 !important;
    }

    .base{
      position: relative;
      padding: 0.12rem 0 0.12rem 0.36rem;

      .icon_imp{
        position: absolute;
        top: 0.16rem;
        right: 0;
        z-index: 50;
      }
      .icon_radio{
        width: 0.2rem;
        height: 0.2rem;
        padding: 0;
        position: absolute;
        top: 0.14rem;
        left: 0;
        z-index: 50;
      }

      h5{
        font-size: @font-form-size;
        line-height: 1.5;
        font-weight: 500;
        margin-bottom: 0.02rem;
        padding-right: 0.2rem;
      }
      p{
        font-size: @font-base-size;
        line-height: 1.42;
        color: @t-color-lightgray;
      }
    }
    .cont{
      border-top: 1px solid @border-color;
      padding: 0.16rem 0;
    }
  }
  &.spel{
    .item{
      .base{
        padding-left: 0;
      }
      .cont{
        padding-top: 0;
        border-top: 0 none;
      }
    }
  }

}
.com_title{
  &.imp_bg{
    background: #FFFBE6;
    color: @imp_color;
  }
}
.notice_box{
  &.risk{
    .pic{
      width: 1.08rem;
      height: 1.08rem;
      margin: 0 auto 0.2rem;
    }
  }
}
.test_level_2{
  width: 1.2rem;
  height: 1.2rem;
  margin-left: 0.4rem;
  position: relative;
  background: url(../images/test_level_bg.png) no-repeat center;
  background-size: 1.1rem;

  .txt{
    width: 100%;
    text-align: center;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: 0;
    z-index: 50;

    h5 {
      height: 0.44rem;
      line-height: 0.44rem;
      font-size: @font-base-size;
      font-weight: normal;
      color: #ffffff;

      strong {
        font-size: 0.38rem;
      }
    }
  }
}
.test_level_txt{
  padding: 0 0.16rem;

  .item{
    padding: 0.2rem 0;
    border-bottom: 1px solid @border-color;
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-gray;

    &:last-child{
      border-bottom: 0 none;
    }

    h5{
      font-size: @font-base-size;
      line-height: 1.42;
      font-weight: 500;
      color: @t-color-normal;
      margin-bottom: 0.08rem;
      color: @t-color-normal;
    }
  }
}
.main.fixed article.content.top_border{
  border-top: 0 none !important;
}
.test_rsult + .rule_check{
  margin-top: 0;
}
.acct_nodata{
  padding: 0.32rem 0.5rem 0.3rem;
  text-align: center;

  .icon{
    width: 1.8rem;
    margin: 0 auto 0.2rem;

    img{
      display: block;
      width: 100%;
    }
  }
  h5{
    font-size: @font-form-size;
    line-height: 1.5;
    font-weight: normal;
  }
}
.pre_selelist{
  .item{
    background: #ffffff;
    padding: 0.12rem 0.44rem 0.12rem 0.16rem;
    position: relative;
    margin-top: 0.08rem;

    &:after{
      content: "\e619";
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: @border-deep-color;
      width: 0.16rem;
      height: 0.16rem;
      font-size: 0.16rem;
      line-height: 0.16rem;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: 0.16rem;
    }

    &:first-child{
      margin-top: 0 !important;
    }

    h5{
      font-size: @font-form-size;
      line-height: 1.5;
      font-weight: normal;
    }
    p{
      font-size: @font-base-size;
      line-height: 1.5;
      color: @t-color-lightgray;
    }
  }
}
.t1_layout{
  display: flex;
  justify-content: right;

  .t1{
    padding: 0 !important;
    height: 0.24rem;
    flex: 1;
  }
  .com_link{
    margin-left: 0.16rem;
  }
}
.tag_selebox{
  margin-top: -0.1rem;
  font-size: 0;

  .tag_item{
    display: inline-block;
    vertical-align: top;
    margin: 0.08rem 0.08rem 0 0;
    padding: 0.04rem 0.12rem;
    font-size: @font-base-size;
    line-height: 1.42;
    background: @bg-color;
    border-radius: 0.5rem;
    color: @t-color-lightgray;

    &.active{
      background: @button-assist-bg;
      color: @button-bg-1;
    }
  }
}
.sub_tipbox{
  background: @border-color;
  padding: 0.12rem;
  border-radius: 0.04rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @t-color-lightgray;
}
.input_text {
  .sub_tipbox{
    margin-top: 0.12rem;
    position: relative;
    top: -0.12rem;
  }
}
.vertical_layout{
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
}

.file_scrollbox{
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  padding: 0 0.16rem;
}
.file_sendform{
  background: #ffffff;
  padding-bottom: 0.24rem;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 50;
}

.form_tit_right{
  .input_text.text{
    .dropdown + .error_tips{
      padding-right: 0.24rem;
    }
  }
}
.qq_acct_wrap{
  padding: 0 0.16rem;
}
.acct_status_item + .qq_acct_wrap{
  margin-top: -0.08rem;
}
.qq_acct_item{
  background: @bg-color;
  border-radius: 0.06rem;
  margin-top: 0.12rem;
  padding: 0.2rem;

  &:first-child{
    margin-top: 0;
  }
  h5{
    font-size: @font-form-size;
    line-height: 1.5;
    font-weight: 500;
    color: @t-color-normal;
    margin-bottom: 0.16rem;

    strong{
      display: block;
      font-weight: 500;
    }
    em{
      display: block;
      font-size: @font-base-size;
      color: @t-color-lightgray;
    }

  }
  .info{
    display: flex;
    flex-wrap: wrap;

    dl{
      width: 50%;
      margin-top: 0.16rem;
      line-height: 1.5;

      &:nth-child(1),
      &:nth-child(2){
        margin-top: 0;
      }
      dt{
        font-size: @font-base-size;
        color: @t-color-lightgray;
      }
      dd{
        font-size: @font-form-size;
      }
    }
  }
}
.qq_acct_wrap + .tip_txtbox{
  border-top: 0 none;
}
.amount_info{
  background: #ffffff;
  padding: 0.16rem 0.16rem 0.14rem;

  .tit{
    font-size: @font-base-size;
    line-height: 1.42;
    font-weight: normal;
  }
  .amount_input{
    display: flex;
    align-items: center;
    padding: 0.1rem 0;

    .icon{
      display: block;
      font-size: 0.3rem;
      margin-right: 0.12rem;
      line-height: 1;
      font-style: normal;
      font-weight: 500;
      color: @t-color-normal;
    }
    .t1{
      display: block;
      flex: 1;
      width: 100%;
      padding: 0.08rem 0;
      font-size: 0.28rem;
      line-height: 0.28rem;
      outline: none;
      border: 0 none;
      font-weight: 500;
      color: @t-color-normal;

      &::-moz-placeholder {
        font-size: @font-form-size;
        line-height: 0.24rem;
      }

      &::-webkit-input-placeholder {
        font-size: @font-form-size;
        line-height: 0.24rem;
      }
    }
    .com_link{
      font-size: @font-base-size;
      line-height: 1.42;
    }
  }
  .tips{
    border-top: 1px solid @border-color;
    padding-top: 0.14rem;
    font-size: @font-base-size;
    line-height: 1.42;
    color: @t-color-lightgray;
    display: flex;
    justify-content: right;

    p{
      flex: 1;
      width: 100%;
    }
    .now_num{
      margin-left: 0.16rem;
    }
  }
}
.error_span{
  color: @error-color !important;
}

.dialog_cont{
  .test_box{
    padding: 0;
    text-align: left;
    margin-bottom: 0;

    .title{
      border-bottom: 0 none;
      font-size: 0.14rem;
      padding-top: 0;
    }

    .radio_list{
      padding: 0;

      .icon_radio,
      .icon_check{
        padding-top: 0.08rem;
        padding-bottom: 0.08rem;
        font-size: 0.14rem;
        min-height: auto;

        &:before{
          top: 0.11rem;
        }
      }
    }
  }
}
.pro_sl_item{
  background: #ffffff;
  margin-bottom: 0.08rem;
  padding: 0 0.16rem;

  .tit{
    padding: 0.12rem 0;
    line-height: 1.5;
    color: @t-color-lightgray;

    h5{
      font-size: @font-form-size;
      font-weight: 500;
      color: @t-color-normal;
    }
  }
  .info{
    border-top: 1px solid @border-color;
    padding: 0.14rem 0;
    line-height: 0.28rem;
    color: @t-color-lightgray;
    display: flex;

    .row{
      flex: 1;
      width: 100%;
    }
    .state{
      margin-right: 0.12rem;
      color: @t-color-lightgray;

      &.ok{
        color: @suc-color;
      }
      &.error{
        color: @error-color;
      }
      &.finish{
        color: @link-color;
      }
    }
  }
  .review_ing{
    color: @error-color;
  }
  .time:nth-child(2){
    flex: 1;
    text-align: right;
  }
}
.com_btn{
  display: inline-block;
  padding: 0.04rem 0.12rem;
  border-radius: 0.5rem;
  font-size: @font-base-size;
  line-height: 1.42;
  color: @button-bg-1;
  background: @main-color-10;
}
.p_com_list{
  padding: 0 0.16rem;

  li{
    border-bottom: 1px solid @border-color;
    display: flex;
    align-items: center;
    padding: 0.12rem 0;

    .list_item_meta{
      flex: 1;
      width: 100%;
      font-size: @font-base-size;
      line-height: 1.5;

      h5{
        font-size: @font-form-size;
        font-weight: 500;
      }
      p{
        color: @t-color-lightgray;
      }
    }
    .arrow{
      display: block;
      width: 0.16rem;
      height: 0.16rem;
      margin-left: 0.2rem;
      font-family: "wt-iconfont" !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: @border-deep-color;
      font-size: 0.16rem;
      line-height: 0.16rem;

      &:after {
        content: "\e619";
      }
    }
  }
}


/*-- add 20230728 --*/
.assist_upload_wrap{
  padding: 0.16rem 0.16rem 0.4rem;

  .tips{
    color: @t-color-lightgray;

    + .assist_upload_item{
      margin-top: 0.24rem;
    }
  }
}
.assist_upload_item{
  margin: 0.12rem auto;
  width: 2.63rem;
  position: relative;
  height: 1.64rem;
  border-radius: 0.08rem;
  overflow: hidden;

  .pic{
    background: #F0F2F7;
    display: flex;
    height: 1.64rem;
    justify-content: center;
    align-items: center;
    align-content: center;

    img{
      display: block;
      max-width: 100%;
      max-height: 100%;
    }
  }
  .tit{
    width: 100%;
    padding: 0 0.15rem;
    font-size: 0.2rem;
    line-height: 1.5;
    color: #CFD3E2;
    font-weight: 700;
    text-align: center;
    position: absolute;
    top: 0.32rem;
    left: 0;

    span{
      display: inline-block;
      vertical-align: top;
      padding-bottom: 0.11rem;
      position: relative;

      &:before{
        content: '';
        width: 100%;
        height: 0.03rem;
        background: #D2D7E5;
        border-radius: 3px;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
  .btn{
    display: inline-block;
    width: 1.4rem;
    height: 0.44rem;
    line-height: 0.44rem;
    background: @button-bg-1;
    border-radius: 0.5rem;
    color: #ffffff;
    box-shadow: 0px 4px 8px 0px rgba(240, 57, 47, 0.25);
    font-size: @font-form-size;
    text-align: center;
    position: absolute;
    bottom: 0.3rem;
    left: 50%;
    margin-left: -0.7rem;
    z-index: 10;

    &:before{
      content: '';
      display: inline-block;
      vertical-align: top;
      width: 0.2rem;
      height: 0.44rem;
      background: url(../images/icon_photo04.png) no-repeat center;
      background-size: 100% auto;
      margin-right: 0.06rem;
    }
  }
  .reset_btn{
    padding: 0 0.14rem;
    left: auto;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    right: 0.1rem;
    bottom: 0.1rem;

    &:before{
      display: none;
    }
  }
}
.upload_addbtn{
  display: block;
  width: 2.63rem;
  margin: 0.24rem auto 0;
  padding: 0.11rem 0.2rem;
  text-align: center;
  font-size: 0.15rem;
  line-height: 0.2rem;
  color: @button-bg-1;
  border: 1px dashed @button-bg-1;
  border-radius: 0.04rem;

  i{
    display: inline-block;
    vertical-align: top;
    position: relative;
    top: 0.02rem;
    width: 0.16rem;
    height: 0.16rem;
    background: url(../images/icon_addbtn.png) no-repeat center;
    background-size: 100%;
    margin-right: 0.08rem;
  }
}
.amount_info{
	.zs{
		margin-top: 0.04rem;
		color: @t-color-lightgray;
	}
}
.watermark {
	width: 0.5rem;
	height: 0.5rem;
	background: url(../images/ic_watermark.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.12rem;
	right: 0.12rem;
	z-index: 50;
}


/*-- add 20230829 --*/
.pro_sl_item .info .state.finish{
	color: #FFB415;
}


/*-- add 20230831 --*/
.bus_txtbox{
	margin: 0 0.16rem 0.16rem;
	font-size: @font-base-size;
	line-height: 1.42;
	color: @t-color-gray;
}
.market_ctlist{
	margin: 0.16rem;

	li{
		background: #ffffff;
		border-radius: 0.06rem;
		margin-top: 0.16rem;

		&:first-child{
			margin-top: 0;
		}
		.base{
			padding: 0.16rem;
			overflow: hidden;
			position: relative;
			z-index: 5;

			h5{
				font-size: @font-title-size;
				line-height: 1.33333;
				font-weight: 500;
			}
			p{
				font-size: @font-base-size;
				line-height: 1.57;
				color: @t-color-lightgray;
				margin-top: 0.08rem;
			}
			.bg{
				height: 100%;
				position: absolute;
				right: 0.3rem;
				left: 0.3rem;
				top: 0;
				z-index: -1;
				display: flex;
				justify-content: right;

				img{
					display: block;
					height: 100%;
					max-width: 100%;
				}
			}
			.state{
				font-size: @font-base-size;
				line-height: 1.7;
				color: @t-color-lightgray;
				position: absolute;
				top: 0.16rem;
				right: 0.16rem;
			}
		}
		.opea{
			border-top: 1px solid @border-color;
			padding: 0.14rem 0.16rem;
			text-align: right;
			font-size: @font-base-size;
			line-height: 1.42;

			.com_btn{
				vertical-align: top;
			}
		}
	}
}
.com_infolist{
	&.spel{
		margin-bottom: 0.16rem;
		li{
			.tit{
				color: @t-color-normal;
			}
			&:last-child{
				border-bottom: 0 none;
			}
		}
	}
}
.cx_acct_txt{
	margin: 0.12rem 0;
	font-size: @font-form-size;
	line-height: 1.5;
	padding: 0.12rem 0.16rem;
	text-align: center;
	background: #F4F4F4;
	color: @t-color-normal;
	border-radius: 0.04rem;
}

.p_button {
  &.link {
    background: none;
    color: @button-bg-1;
	  margin-top: 0 !important;
  }
}
.backWhite_box {
  background: #ffffff;
}


/*-- add 20230901 --*/
.foot_tipbox{
	text-align: center;
	padding: 0.1rem 0.16rem;
	color: @t-color-gray;
}
.market_ctlist{
	li{
		.num{
			color: @t-color-normal;
		}
		.acct_s_tag{
			border-color: @border-deep-color;
			color: @border-deep-color;
		}
		.opea{
			.com_btn{
				margin-left: 0.08rem;
			}
		}
		.error{
			color: @error-color;
		}
	}
}
.com_btn.border{
	border: 1px solid @button-bg-1;
	background: none;
	color: @button-bg-1;
	padding: 0.03rem 0.11rem;
}
.top_error_tips{
	padding: 0.1rem 0.16rem;
	display: flex;
	background: #FFF0F0;
	font-size: @font-base-size;
	line-height: 1.42;
	color: @error-color;

	p{
		flex: 1;
		min-width: 0;
		padding: 0.04rem 0 0.04rem 0.28rem;
		position: relative;

		&:before{
			content: "\e654";
			width: 0.2rem;
			height: 0.2rem;
			font-size: 0.2rem;
			line-height: 1;
			color: @error-color;
			font-family: "wt-iconfont" !important;
			font-style: normal;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			position: absolute;
			top: 0.04rem;
			left: 0;
		}
	}
	.com_btn{
		margin-left: 0.15rem;
	}
}
.zh_acct_list{
	li{
		border-bottom: 1px solid @border-color;
		padding: 0.12rem 0 0.12rem 0.3rem;
		position: relative;

		&:last-child{
			border-bottom: 0 none;
		}
		.icon_radio{
			display: block;
			width: 0.2rem;
			height: 0.2rem;
			padding: 0;
			position: absolute;
			top: 0.14rem;
			left: 0;
			z-index: 50;

			&:before{
				top: 50%;
				left: 50%;
				margin: -0.09rem 0 0 -0.09rem;
			}
		}
		.state{
			font-size: @font-base-size;
			line-height: 1.7;
			color: @t-color-lightgray;
			position: absolute;
			top: 0.12rem;
			right: 0;
		}
		h5{
			font-size: @font-form-size;
			line-height: 1.5;
			font-weight: normal;
		}
		.ct{
			font-size: @font-base-size;
			line-height: 0.2rem;
			margin-top: 0.04rem;
			color: @t-color-lightgray;
			position: relative;

			.txt{
        white-space: normal;  // 允许文本自动换行
        word-wrap: break-word;  // 允许长单词断行
        word-break: break-all;  // 在任意字符间断行
        overflow: visible;  // 显示溢出内容
        position: relative;
        padding-right: 0.55rem;

				.com_link{
					position: absolute;
					right: 0;
          bottom: 0;
          z-index: 50;
				}
				&.on{
					display: block;
					overflow: visible;
				}

			}

		}
		.error_tips{
			margin-top: 0.02rem;
			top: 0;
		}
	}
}

.acct_add_cont{
	.input_text + .error_tips{
		top: 0;
		margin-top: 0;
		padding: 0.14rem 0;
	}
}
.cond_info_layout{
	justify-content: right;
}
.cond_list li.warn .tit h5:before{
	content: "\e61e";
	color: @imp_color;
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}
.form_tips{
	margin: 0.16rem;
	font-size: @font-base-size;
	line-height: 1.42;
	color: @t-color-gray;
}
.mt40{
	margin-top: 0.4rem;
}
.queue_level{
	.bg{
		background: none;
		border: 3px solid @main-color;

		&:before{
			content: '';
			background: @main-color;
			opacity: 0.03;
			border-radius: 50%;
			position: absolute;
			top: -3px;
			left: -3px;
			right: -3px;
			bottom: -3px;
			z-index: 10;
		}
		&:after{
			content: '';
			background: url(../images/queue_bg2.png) no-repeat center;
			background-size: 100% 100%;
			border-radius: 50%;
			position: absolute;
			top: -4px;
			left: -4px;
			right: -4px;
			bottom: -4px;
			z-index: 5;
		}
	}
}
.tip_txtbox.spel{
	background: none;
	padding: 0;
	margin: 0.16rem;
	color: @t-color-gray;
}
.mb0{
	margin-bottom: 0 !important;
}
.p_button.border-2{
	color: @button-bg-1;
	border: 1px solid @button-bg-1;
	background: none;

	&.disabled{
		border: 0 none;
		background: @disbaled-bg !important;
		color: #ffffff !important;
	}
}
.p_cm_list{
	background: #ffffff;
	padding: 0 0.16rem;
	margin: 0.08rem 0;

	li{
		border-bottom: 1px solid @border-color;
		display: flex;
		align-items: center;
		padding: 0.16rem 0;
		font-size: @font-form-size;
		line-height: 1.5;

		&:last-child{
			border-bottom: 0 none;
		}
		.ct{
			flex: 1;
			min-width: 0;
		}
		.arrow{
			display: block;
			width: 0.16rem;
			height: 0.16rem;
			font-family: "wt-iconfont" !important;
			font-style: normal;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			color: @border-deep-color;
			font-size: 0.16rem;
			line-height: 1;
			margin-left: 0.15rem;

			&:before{
				content: "\e619";
			}
		}
	}
}
.txt_explain{
	font-size: @font-base-size;
	line-height: 1.7;
	color: @t-color-normal;
	padding: 0 0.16rem;

	h5{
		font-size: @font-form-size;
		margin: 0.16rem 0 0.1rem;
		font-weight: 500;
		line-height: 1.5;
	}
	p{
		margin: 0.1rem 0;
	}
	.img_wrap{
		margin: 0.1rem 0 0.2rem;

		img{
			display: block;
			margin: 0 auto;
			max-width: 100%;
		}
	}
}
.pl60{
	padding-left: 0.6rem !important;
}

.start_date,.end_date{
  display: inline;
}
/*-- add 20230901 --*/
.foot_tipbox {
  text-align: center;
  padding: 0.1rem 0.16rem;
  color: #666666;
}
.market_ctlist li .num {
  color: #333333;
}
.market_ctlist li .acct_s_tag {
  border-color: #BBBBBB;
  color: #BBBBBB;
}
.market_ctlist li .opea .com_btn {
  margin-left: 0.08rem;
}
.market_ctlist li .error {
  color: #FF4848;
}
.com_btn.border {
  border: 1px solid #FF2840;
  background: none;
  color: #FF2840;
  padding: 0.03rem 0.11rem;
}
.top_error_tips {
  padding: 0.1rem 0.16rem;
  display: flex;
  background: #FFF0F0;
  font-size: 0.14rem;
  line-height: 1.42;
  color: #FF4848;
}
.top_error_tips p {
  flex: 1;
  min-width: 0;
  padding: 0.04rem 0 0.04rem 0.28rem;
  position: relative;
}
.top_error_tips p:before {
  content: "\e654";
  width: 0.2rem;
  height: 0.2rem;
  font-size: 0.2rem;
  line-height: 1;
  color: #FF4848;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 0.04rem;
  left: 0;
}
.top_error_tips .com_btn {
  margin-left: 0.15rem;
}
.zh_acct_list li {
  border-bottom: 1px solid rgba(221, 221, 221, 0.5);
  padding: 0.12rem 0 0.12rem 0.3rem;
  position: relative;
}
.zh_acct_list li:last-child {
  border-bottom: 0 none;
}
.zh_acct_list li .icon_radio {
  display: block;
  width: 0.2rem;
  height: 0.2rem;
  padding: 0;
  position: absolute;
  top: 0.14rem;
  left: 0;
  z-index: 50;
}
.zh_acct_list li .icon_radio:before {
  top: 50%;
  left: 50%;
  margin: -0.09rem 0 0 -0.09rem;
}
.zh_acct_list li .state {
  font-size: 0.14rem;
  line-height: 1.7;
  color: #999999;
  position: absolute;
  top: 0.12rem;
  right: 0;
}
.zh_acct_list li h5 {
  font-size: 0.16rem;
  line-height: 1.5;
  font-weight: normal;
}
.zh_acct_list li .ct {
  font-size: 0.14rem;
  line-height: 0.2rem;
  margin-top: 0.04rem;
  color: #999999;
  position: relative;
}
.zh_acct_list li .ct .txt {
  /* max-height: 0.4rem;
  overflow: hidden; */
  position: relative;
}
.zh_acct_list li .ct .txt span {
  display: block;
}
.zh_acct_list li .ct .txt .more {
  background: #ffffff;
  padding-left: 0.03rem;
  position: absolute;
  right: 0;
  top: 0.2rem;
  z-index: 50;
}
.zh_acct_list li .ct .txt .more .com_link {
  margin-left: 0.05rem;
}
.zh_acct_list li .ct .txt.on {
  display: block;
  overflow: visible;
}
.zh_acct_list li .error_tips {
  margin-top: 0.02rem;
  top: 0;
}
.acct_add_cont .input_text + .error_tips {
  top: 0;
  margin-top: 0;
  padding: 0.14rem 0;
}
.cond_info_layout {
  justify-content: right;
}
.cond_list li.warn .tit h5:before {
  content: "\e61e";
  color: #FFB415;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.form_tips {
  margin: 0.16rem;
  font-size: 0.14rem;
  line-height: 1.42;
  color: #666666;
}
.mt40 {
  margin-top: 0.4rem;
}
.queue_level .bg {
  background: none;
  border: 3px solid #FF2840;
}
.queue_level .bg:before {
  content: '';
  background: #FF2840;
  opacity: 0.03;
  border-radius: 50%;
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  z-index: 10;
}
.queue_level .bg:after {
  content: '';
  background: url(../images/queue_bg2.png) no-repeat center;
  background-size: 100% 100%;
  border-radius: 50%;
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  z-index: 5;
}
.tip_txtbox.spel {
  background: none;
  padding: 0;
  margin: 0.16rem;
  color: #666666;
}
.mb0 {
  margin-bottom: 0 !important;
}
.p_button.border-2 {
  color: #FF2840;
  border: 1px solid #FF2840;
  background: none;
}
.p_button.border-2.disabled {
  border: 0 none;
  background: #CCCCCC !important;
  color: #ffffff !important;
}
.p_cm_list {
  background: #ffffff;
  padding: 0 0.16rem;
  margin: 0.08rem 0;
}
.p_cm_list li {
  border-bottom: 1px solid rgba(221, 221, 221, 0.5);
  display: flex;
  align-items: center;
  padding: 0.16rem 0;
  font-size: 0.16rem;
  line-height: 1.5;
}
.p_cm_list li:last-child {
  border-bottom: 0 none;
}
.p_cm_list li .ct {
  flex: 1;
  min-width: 0;
}
.p_cm_list li .arrow {
  display: block;
  width: 0.16rem;
  height: 0.16rem;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #BBBBBB;
  font-size: 0.16rem;
  line-height: 1;
  margin-left: 0.15rem;
}
.p_cm_list li .arrow:before {
  content: "\e619";
}
.txt_explain {
  font-size: 0.14rem;
  line-height: 1.7;
  color: #333333;
  padding: 0 0.16rem;
}
.txt_explain h5 {
  font-size: 0.16rem;
  margin: 0.16rem 0 0.1rem;
  font-weight: 500;
  line-height: 1.5;
}
.txt_explain p {
  margin: 0.1rem 0;
}
.txt_explain .img_wrap {
  margin: 0.1rem 0 0.2rem;
}
.txt_explain .img_wrap img {
  display: block;
  margin: 0 auto;
  max-width: 100%;
}
.pl60 {
  padding-left: 0.6rem !important;
}
/*-- add 20230908 --*/
.protocol_box.box_bg {
  background: #f8f8f8;
  margin: 0.16rem;
  border-radius: 0.04rem;
  padding: 0.16rem 0 0;
}
.protocol_box.box_bg .title {
  padding: 0.06rem 0.16rem 0.16rem;
  font-size: 0.18rem;
  margin-bottom: 0;
}
.protocol_box.box_bg .protocol_cont {
  padding: 0.06rem 0.16rem 0.16rem;
  max-height: 2.8rem;
  overflow: auto;
}
/*-- add 20230915 start --*/
.cm_sele_wrap {
  background: #ffffff;
  margin: 0.08rem 0;
  padding: 0 0.16rem;
}
.cm_sele_wrap .title {
  padding: 0.16rem 0;
  font-size: 0.16rem;
  line-height: 1.5;
  font-weight: 500;
}
.cm_sele_wrap .title + .cm_sele_list {
  border-top: 1px solid rgba(221, 221, 221, 0.5);
}
.com_title + .cm_sele_wrap {
  margin-top: 0;
}
.cm_sele_list li {
  border-bottom: 1px solid rgba(221, 221, 221, 0.5);
  padding: 0.16rem 0;
  font-size: 0.16rem;
  line-height: 1.5;
}
.cm_sele_list li .layout {
  display: flex;
  flex-direction: row;
}
.cm_sele_list li .layout .icon_radio,
.cm_sele_list li .layout .icon_check {
  margin-right: 0.06rem;
}
.cm_sele_list + .notes_input {
  background: #ffffff;
  margin-top: -1px;
  position: relative;
}
.notes_input textarea {
  display: block;
  width: 100%;
  overflow: auto;
  resize: none;
  outline: none;
  border: 0 none;
  background: #F4F4F4;
  border-radius: 0.04rem;
  padding: 0.12rem;
  font-family: -apple-system-font, "Helvetica Neue", sans-serif;
  font-size: 0.16rem;
  line-height: 1.5;
  color: #333333;
  height: 0.72rem;
}
.small_tit {
  display: block;
  font-size: 0.14rem;
  color: #999999;
  line-height: 1.5;
  margin-bottom: 0.02rem;
}
.acct_list.xh li .icon_radio,
.acct_list.xh li .icon_check {
  padding-left: 0;
  padding-right: 0.3rem;
}
.acct_list.xh li .icon_radio:before,
.acct_list.xh li .icon_check:before {
  left: auto;
  right: 0;
}
.xh_pword_input {
  padding-bottom: 0.08rem;
  position: relative;
  top: -0.04rem;
}
.xh_pword_input input {
  display: block;
  width: 100%;
  height: 0.48rem;
  padding: 0 0.16rem;
  font-size: 0.16rem;
  color: #333333;
  border-radius: 0.04rem;
  border: 0 none;
  background: #F4F4F4;
  outline: none;
}
.imp_c_tips.no_icon p {
  padding-left: 0;
}
.imp_c_tips.no_icon p:before {
  display: none;
}
.result_tips + .imp_c_tips {
  margin: 0 0.16rem;
  border-top: 1px solid rgba(221, 221, 221, 0.5);
}
.result_status_item {
  background: #ffffff;
  border-top: 0.08rem solid #F4F4F4;
}
.result_status_item:last-child {
  margin-bottom: 0.08rem;
}
.result_status_item .h_tit {
  margin: 0 0.16rem;
  position: relative;
  font-size: 0.16rem;
  line-height: 1.5;
  font-weight: 500;
}
.result_status_item .h_tit h5 {
  padding: 0.16rem 0.6rem 0.16rem 0;
  font-size: 0.16rem;
  line-height: 1.5;
  font-weight: 500;
  color: #333333;
}
.result_status_item .h_tit .drop_arrow {
  position: absolute;
  top: 50%;
  margin-top: -0.08rem;
  right: 0;
}
.fileUpload_scrollback {
  // max-height: calc(100vh - 100px);
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
.leave_mark {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0 0.16rem;
  margin: 0.2rem 0;
  // position: fixed;
  // left: 0;
  // bottom: 0.56rem;
}
.leaceMarkCheckbox {
  width: 0.4rem;
  margin-top: 0.03rem;
}

/*-- add 20231206 --*/
.limit_infobox{
	margin: 0.16rem;
	border: 1px solid #FFF;
	background: linear-gradient(180deg, #FFF5F5 0%, #FFF 46.35%);
	border-radius: 0.06rem;
	padding: 0.28rem 0.2rem;
	text-align: center;
	color: @t-color-gray;

	.num{
		font-size: 0.3rem;
		line-height: 1.2;
		font-weight: 600;
		color: @main-color;
		margin-top: 0.08rem;
	}
	.info{
		margin-top: 0.13rem;
	}
}
.limit_tag{
	display: inline-block;
	vertical-align: top;
	padding: 0.06rem 0.18rem;
	font-size: @font-base-size;
	line-height: 0.2rem;
	color: @t-color-normal;
	background: @bg-color;
	position: relative;
	border-radius: 0.5rem;

	&:before{
		content: '';
		width: 0;
		height: 0;
		border-style: solid;
		border-width: 0 4px 5px;
		border-color: transparent transparent @bg-color;
		position: absolute;
		top: -5px;
		left: 50%;
		margin-left: -4px;
	}
}

/*-- add 2-231211 --*/
.foot_tips{
	text-align: center;
	padding: 0.16rem;
	font-size: @font-base-size;
	line-height: 1.42;
	color: @t-color-gray;
}
.ce_btn + .foot_tips{
	padding-top: 0.1rem;
}
.tj_video_page{
	padding: 0.16rem;
	font-size: @font-base-size;
	line-height: 1.5;
	color: @t-color-gray;

	p{
		strong{
			color: @t-color-normal;
		}
	}
}
.tj_video_window{
	margin: 0.26rem 0;
	overflow: hidden;
	border-radius: 0.08rem;

	video{
		display: block;
		width: 100%;
		background: #333333;
	}
}
.lr_spel_item{
	background: #ffffff;
	margin-bottom: 0.08rem;

	.base{
		padding: 0.16rem;
		display: flex;
		align-items: center;
		font-size: 0.16rem;
		line-height: 1.5;

		h5{
			font-size: 0.16rem;
			font-weight: 500;
			flex: 1;
			min-width: 0;
		}
		.opea{
			margin-left: 0.4rem;

			.icon_check{
				margin-left: 0.16rem;

				&:before{
					margin-left: 0;
				}
			}
		}
	}
}
.lr_spel_cont{
	position: relative;

	&:before{
		content: '';
		height: 1px;
		background: @border-color;
		position: absolute;
		top: 0;
		left: 0.16rem;
		right: 0.16rem;
		z-index: 5;
	}
	.com_box{
		border-bottom: 0.04rem solid @bg-color;

		&:last-child{
			border-bottom: 0 none;
		}
	}
	.input_text.text{
		.t1,
		.dropdown,
		.tarea1{
			padding-left: 1.08rem;
		}
	}

}
.opea_ctbox{
	overflow: hidden;

	.add_btn_01{
		float: left;
		margin-left: 0;
	}
	.delete_btn_01{
		float: right;
	}
}
.result_tips{
	padding-left: 0.16rem;
	padding-right: 0.16rem;
}
.result_step{
	padding: 0.24rem 0;
	margin-bottom: 0.2rem;
	border-bottom: 1px solid @border-color;

	ul{
		margin-left: -0.16rem;
		margin-right: -0.16rem;
	}
}

/*-- add 20231213 --*/
.date_filter_wrap{
	height: 0.56rem;
	overflow: hidden;
}
.date_filter_nav{
	padding: 0.14rem 0.16rem;
	overflow: auto;
	height: 0.7rem;
	display: flex;

	li{
		margin-left: 0.08rem;

		&:first-child{
			margin-left: 0;
		}
		span{
			display: inline-block;
			vertical-align: top;
			padding: 0.03rem 0.12rem;
			border: 1px solid @bg-color;
			border-radius: 0.5rem;
			font-size: @font-base-size;
			line-height: 0.2rem;
			color: @t-color-lightgray;
			background: @bg-color;
			white-space: nowrap;
		}
		&.active{
			span{
				color: @main-color;
				border-color: @main-color;
				background: #ffffff;
			}
		}
	}
}
.p_right_arrow{
	display: inline-block;
	vertical-align: top;
	font-family: "wt-iconfont" !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	color: @border-deep-color;
	width: 0.16rem;
	height: 0.16rem;
	font-size: 0.16rem;
	line-height: 0.16rem;

	&:before{
		content: "\e619";
	}
}
.bus_ratelist li{
	.p_right_arrow{
		margin-left: 0.04rem;
	}
}
.acct_nodata{
	color: @t-color-lightgray;
}
.p_rate_wrap{
	padding: 0.3rem 0.2rem 0.2rem;

	.reject_txtinfo{
		border-top: 0 none;
		margin: 0.24rem 0 0;
		padding: 0;
	}
}
.p_rate_progress{
	li{
		padding: 0 0 0.24rem 0.3rem;
		position: relative;

		&:last-child{
			padding-bottom: 0;
		}
		h5{
			font-size: @font-form-size;
			line-height: 0.22rem;
			font-weight: 500;
		}
		p{
			color: @t-color-lightgray;
			margin-top: 0.04rem;
		}

		&:before{
			content: '';
			width: 1px;
			height: 100%;
			background: #EDEDED;
			position: absolute;
			top: 0.11rem;
			left: 0.1rem;
		}
		&:last-child:before{
			display: none;
		}
		.icon{
			width: 0.2rem;
			height: 0.2rem;
			border: 1px solid #CCCCCC;
			background: #ffffff;
			border-radius: 50%;
			position: absolute;
			top: 0.01rem;
			left: 0;
			z-index: 5;
		}
		&.ok{
			.icon{
				border: 0 none;
				background: #ffffff;

				&:before{
					content: "\e621";
					display: block;
					width: 0.2rem;
					height: 0.2rem;
					font-size: 0.2rem;
					line-height: 1;
					text-align: center;
					color: @suc-color;
					font-family: "wt-iconfont" !important;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					position: absolute;
					left: 0;
					top: 0;
				}
			}
		}
		&.ing{
			.icon{
				border: 0 none;
				background: #ffffff;

				&:before{
					content: "\e628";
					display: block;
					width: 0.2rem;
					height: 0.2rem;
					font-size: 0.2rem;
					line-height: 1;
					text-align: center;
					color: #CCCCCC;
					font-family: "wt-iconfont" !important;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					position: absolute;
					left: 0;
					top: 0;
				}
			}
		}
		&.error{
			.icon{
				border: 0 none;
				background: #ffffff;

				&:before{
					content: "\e61e";
					display: block;
					width: 0.2rem;
					height: 0.2rem;
					font-size: 0.2rem;
					line-height: 1;
					text-align: center;
					color: @error-color;
					font-family: "wt-iconfont" !important;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					position: absolute;
					left: 0;
					top: 0;
				}
			}
		}
	}
}
.p_rate_tips{
	background: @bg-color;
	padding: 0.14rem;
	border-radius: 0.04rem;
	font-size: @font-base-size;
	line-height: 1.42;
	color: @t-color-normal;
	margin-top: 0.24rem;
}


.tj_audio_page{
	height: 100%;
	display: flex;
	flex-direction: column;
}
.audio_p_wrap{
	background: @bg-color;
	padding: 0 0.16rem 0.16rem;
}
.audio_player{
	background: #ffffff;
	border-radius: 0.08rem;
	padding: 0.16rem;
	display: flex;
	align-items: center;
}
.a_player_button{
	display: block;
	width: 0.32rem;
	height: 0.32rem;
	border-radius: 50%;
	margin-right: 0.21rem;
	background: @button-bg-1;
	position: relative;
}
.a_player_play,
.a_player_pause{
	&:before{
		content: '';
		width: 0.16rem;
		height: 0.16rem;
		position: absolute;
		top: 50%;
		left: 50%;
		-webkit-transform: translate3d(-50%, -50%, 0);
		transform: translate3d(-50%, -50%, 0);
	}
}
.a_player_play:before{
	background: url(../images/a_player_play.png) no-repeat center;
	background-size: 100%;
}
.a_player_pause:before{
	background: url(../images/a_player_pause.png) no-repeat center;
	background-size: 100%;
}
.a_player_controller{
	flex: 1;
	min-width: 0;
	padding-top: 0.07rem;
}
.tj_audio_txt{
	flex: 1;
	min-height: 0;
	overflow: auto;
	padding: 0.16rem;
	font-size: @font-form-size;
	line-height: 1.6;
	color: @t-color-normal;
}
.a_player_bar{
	height: 0.03rem;
	background: #DDDDDD;
	border-radius: 0.03rem;
	position: relative;
	padding-right: 0.09rem;
}
.a_player_played{
	height: 0.03rem;
	background: @main-color;
	border-radius: 0.03rem;
	position: relative;
}
.a_player_thumb{
	width: 0.09rem;
	height: 0.09rem;
	background: @button-bg-1;
	border-radius: 50%;
	box-shadow: 0 0 0 0.03rem @main-color-30;
	position: absolute;
	top: -0.03rem;
	right: 0;
	-webkit-transform: translateX(100%);
	transform: translateX(100%);
	z-index: 5;
}
.a_player_time{
	margin-top: 0.05rem;
	font-size: 0.12rem;
	line-height: 0.17rem;
	color: @t-color-gray;
	display: flex;
	justify-content: space-between;
}
.txt_readed{
	background: @button-assist-bg;
}

/*-- add 20240125 --*/
.pr_0{
	padding-right: 0 !important;
}
.bus_recordlist{
	background: #ffffff;
}
.bus_record_item{
	margin: 0 0.16rem;
	padding: 0.18rem 0 0.16rem;
	border-bottom: 1px solid @border-color;

	&:last-child{
		border-bottom: 0 none;
	}
	.title{
		display: flex;
		align-items: center;

		h5{
			flex: 1;
			min-width: 0;
			font-size: @font-form-size;
			line-height: 1.5;
			font-weight: 500;
		}
		.state{
			margin-left: 0.16rem;
		}
		.p_right_arrow{
			margin-left: 0.08rem;
		}
	}
	.state{
		font-size: @font-base-size;
		line-height: 0.2rem;
		color: @t-color-lightgray;

		&.ing{
			color: @imp_color;
		}
		&.error{
			color: @error-color;
		}
		&.ok{
			color: @suc-color;
		}
	}
	.cont{
		margin-top: 0.14rem;
		background: @bg-color;
		border-radius: 0.04rem;
		padding: 0.08rem 0.12rem;
		font-size: @font-base-size;
		line-height: 0.24rem;
		color: @t-color-gray;
	}
}

/*-- add 20240126 --*/
.email_infobox{
	padding: 0 0.16rem 0.16rem;
	text-align: center;

	.data{
		font-size: 0.24rem;
		line-height: 1.5;
		color: @main-color;
		margin-top: 0.06rem;
	}

	+ .bus_txtbox{
		border-top: 1px solid @border-color;
		padding-top: 0.2rem;
		margin-top: 0.16rem;
		line-height: 1.6;

		p{
			margin-bottom: 0.1rem;
		}
	}
}

/*-- add 20240412 start --*/
.home_pg_bg{
	background: #ffffff url(../images/wt_hm_bg.png) no-repeat center top;
	background-size: 100% auto;

	.header{
		background: none;
	}
}
.icon_msg,
.icon_cs{
	width: 0.4rem;
	height: 0.44rem;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 0.24rem;
	position: absolute;
	top: 0;
	z-index: 50;
}
.icon_cs{
	right: 0.08rem;
	background-image: url(../images/icon_cs.svg);
}
.icon_msg{
	right: 0.48rem;
	background-image: url(../images/icon_msg.svg);

	i{
		min-width: 0.14rem;
		padding: 0 0.02rem;
		height: 0.14rem;
		line-height: 0.14rem;
		text-align: center;
		font-size: 0.1rem;
		font-style: normal;
		color: #ffffff;
		border-radius: 0.14rem;
		background: @main-color;
		position: absolute;
		top: 0.04rem;
		left: 50%;
	}
}
.search_link{
	background: none;
	padding-bottom: 0;
	margin-bottom: 0.16rem;

	a{
		background: #ffffff;
	}
}
.hm_imp_tips{
	margin: 0.16rem 0.2rem;
	padding-left: 0.2rem;
	font-size: 0.13rem;
	line-height: 1.4;
	color: @t-color-gray;
	position: relative;

	.imp{
		color: @imp_color;
	}
	.icon{
		width: 0.14rem;
		height: 0.14rem;
		position: absolute;
		top: 0.02rem;
		left: 0;
		font-size: 0.14rem;
		line-height: 1;
		color: @imp_color;
		font-family: "wt-iconfont" !important;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;

		&:before{
			content: "\e609";
		}
	}
}
.hm_cont_wrap{
	background: #ffffff;
	border-radius: 0.12rem 0.12rem 0 0;
}
.bus_tp_module{
	background: none;
	padding: 0 0 0.24rem;

	&.hot{
		border-radius: 0;
		padding: 0;
		margin-bottom: 0.08rem;
	}
	&:last-child{
		padding-bottom: 0.24rem;
	}
}
.bus_tp_title{
	padding: 0.14rem 0.2rem;
	line-height: 0.28rem;
	margin-bottom: 0;

	.icon{
		display: inline-block;
		vertical-align: top;
		width: 0.2rem;
		height: 0.2rem;
		background: none !important;
		margin-right: 0.08rem;
		position: relative;
		top: 0.04rem;

		img{
			display: block;
			width: 100%;
			height: 100%;
			top: 0;
		}
		&:before{
			content: '';
			display: none;
		}
	}
}
.bus_hot_list{
	padding: 0 0.12rem;
	display: flex;
	flex-wrap: wrap;

	li{
		flex: 0 0 25%;
		width: 25%;
		padding: 0;

		a{
			padding: 0.08rem 0.05rem;
			text-align: center;
			font-size: @font-base-size;
			line-height: 1.2;
			color: @t-color-normal;

			img{
				width: 0.36rem;
				height: 0.36rem;
				margin: 0 auto 0.06rem;
			}
		}
	}
}
.hm_adv_box{
	margin: 0.08rem 0.2rem 0.24rem;
	border-radius: 0.1rem;
	overflow: hidden;

	img{
		display: block;
		width: 100%;
		border-radius: 0.1rem;
	}
}
.bus_type_wrap{
	background: #ffffff;
	padding: 0;
	margin: 0 0 0.24rem;
	border-bottom: 1px solid @border-color;
	overflow: hidden;
}
.bus_type_nav{
	padding: 0 0 0.2rem;

	li{
		margin-left: 0;

		span{
			width: 0.94rem;
			text-align: center;
			height: 0.48rem;
			line-height: 0.48rem;
			border-radius: 0;
			background: none !important;
			padding: 0;
			font-size: @font-base-size;
			color: @t-color-gray;
			position: relative;
			white-space: nowrap;
		}

		&.active{
			span{
				color: @t-color-normal;
				font-size: @font-form-size;
				font-weight: 500;

				&:after{
					content: '';
					width: 0.32rem;
					height: 0.03rem;
					border-radius: 0.03rem;
					background: @main-color;
					position: absolute;
					bottom: 0;
					left: 50%;
					margin-left: -0.16rem;
				}
			}
		}
	}
}
.bus_type_fixed{
	.bus_type_wrap{
		margin: 0;
		padding: 0;
	}
}
.bus_navlist{
	li{

		a{
			line-height: 0.2rem;
			padding-left: 0.3rem;
			padding-right: 0.2rem;

			&:before{
				background: #CCCCCC;
				top: 0.2rem;
				margin-top: 0;
			}
		}
	}
}
.bus_ic_new{
	vertical-align: top;
	position: relative;
	right: 0;
	bottom: 0;
	top: 0;
}
.top_searchbox{
	.btn{
		color: @t-color-gray;
	}
	.txt_close{
		right: 0.64rem;
	}
}
.bus_search_module{
	padding-top: 0.12rem;

	.bus_navlist{
		li{
			width: 100%;

			a{
				padding-top: 0.1rem;
				padding-bottom: 0.1rem;

				&:before{
					top: 0.18rem;
				}
			}
		}
	}
}
.txt_close{
	color: #bbbbbb;
}
.nodata_box{
	padding: 0.7rem 0.5rem;

	.icon{
		width: 1.8rem;
		height: auto;
		margin: 0 auto 0.38rem;
	}
	p{
		font-size: 0.15rem;
		line-height: 1.5;
		color: @t-color-gray;
	}
}
.online_tipbox{
	padding: 0.7rem 0.5rem;
	text-align: center;
	font-size: 0.13rem;
	line-height: 1.5;
	color: @t-color-gray;

	.icon{
		width: 1.9rem;
		margin: 0 auto;

		img{
			display: block;
			width: 100%;
		}
	}
	h5{
		font-size: 0.15rem;
		font-weight: normal;
		color: @t-color-normal;
		margin-bottom: 0.08rem;
	}
	.btn_wrap {
	  margin-top: 0.16rem;

	  .com_btn {
	    padding: 0.07rem 0.16rem;
	    min-width: 1.2rem;
	    text-align: center;
	  }
	}
}
.bus_ratelist{
	li{
		&:last-child{
			border-bottom: 1px solid @border-color;
		}
		.row_1{
			padding-left: 0.12rem;
		}
		h5{
			position: relative;

			&:before{
				content: '';
				width: 0.04rem;
				height: 0.04rem;
				border-radius: 50%;
				background: #CCCCCC;
				position: absolute;
				top: 50%;
				margin-top: -0.02rem;
				left: -0.12rem;
			}
		}
	}
}

/*-- add 20240412 end --*/

/*-- add 202404516 start --*/
.main.fixed.qq_bg{
	background: linear-gradient(128deg, #FFCDCD 8.63%, #FFF 38.54%, #FFFBFB 54.4%, #F3F4F6 66.04%);

	.header.fixed_header .icon_back{
		color: @t-color-normal !important;
	}
}
.qq_hm_page{
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: vertical;
	box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column;
	min-height: 100%;
}
.qq_bannerbox{
	min-height: 1rem;
	margin-bottom: -15%;

	img{
		display: block;
		width: 100%;
	}
}
.qq_hm_cont{
	border-radius: 0.09rem;
	border: 1px solid #FFF;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.40) 0%, rgba(255, 255, 255, 0.00) 100%);
	box-shadow: 0 -0.05rem 0.1rem 0 rgba(215, 216, 221, 0.20);
	position: relative;
	z-index: 50;
	flex: 1;
	min-width: 0;
	margin: 0 0.15rem;
}
.qq_hm_title{
	padding: 0.17rem 0.2rem 0.15rem;

	h5{
		font-size: 0.2rem;
		line-height: 0.24rem;
		font-weight: 500;
		color: @main-color;
		display: flex;
		justify-content: center;
		align-items: center;

		span{
			margin: 0 0.1rem;
		}
		i{
			display: block;
			width: 0.4rem;
			height: 1px;
			position: relative;

			&:before{
				content: '';
				width: 100%;
				height: 1px;
				background: linear-gradient(90deg, rgba(255, 255, 255, 0.00) 13.44%, #EEB0A6 95.27%);
				position: absolute;
				top: 0;
				left: 0;
				-webkit-transform: scaleY(0.5);
				transform: scaleY(0.5);
			}
			&:after{
				content: '';
				width: 4px;
				height: 4px;
				background: #EEB0A6;
				position: absolute;
				top: 50%;
				margin-top: -2px;
				-webkit-transform: rotate(45deg);
				transform: rotate(45deg);
				right: 0;
			}
			&:last-child{
				&:before{
					background: linear-gradient(-90deg, rgba(255, 255, 255, 0.00) 13.44%, #EEB0A6 95.27%);
				}
				&:after{
					right: auto;
					left: 0;
				}
			}

		}
	}
}
.qq_hm_form{
	background: #ffffff;
	padding: 0.28rem 0.2rem 0.24rem;
	border-radius: 0.06rem;

	.ce_btn{
		padding: 0;
		margin-top: 0.3rem;

		.p_button{
			box-shadow: 0 0.07rem 0.1rem 0 rgba(255, 56, 77, 0.30);
		}
	}
}
.s_input_item{
	margin-top: 0.12rem;
	position: relative;
	background: #F2F4F8;
	border-radius: 0.06rem;
	min-height: 0.48rem;
	display: flex;
	align-items: center;

	&:first-child{
		margin-top: 0;
	}
	.tit{
		width: 1rem;
		padding-left: 0.12rem;
		font-size: @font-form-size;
		line-height: 1.5;
		position: relative;
		color: @t-color-normal;

		&:before{
			content: '';
			width: 1px;
			height: 0.16rem;
			background: #E3E4E7;
			position: absolute;
			top: 50%;
			margin-top: -0.08rem;
			right: 0;
		}
	}
	.ct{
		flex: 1;
		min-width: 0;
		position: relative;

		.code_btn{
			background: none;
			right: 0.12rem;
		}
	}
	.t1{
		display: block;
		box-sizing: border-box;
		width: 100%;
		height: 0.48rem;
		font-size: @font-form-size;
		color: @t-color-normal;
		outline: none;
		border-radius: 0.06rem;
		padding: 0 0.12rem;
		border: 0 none;
		background: none !important;
	}
	.dropdown{
		display: block;
		box-sizing: border-box;
		width: 100%;
		height: 0.48rem;
		font-size: @font-form-size;
		color: @t-color-normal;
		border-radius: 0.06rem;
		padding: 0 0.32rem 0 0.12rem;
		background: none;
		line-height: 0.48rem;
		position: relative;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;

		&:after {
		  content: "\e619";
		  font-family: "wt-iconfont" !important;
		  font-style: normal;
		  -webkit-font-smoothing: antialiased;
		  -moz-osx-font-smoothing: grayscale;
		  color: @border-deep-color;
		  width: 0.16rem;
		  height: 0.16rem;
		  font-size: 0.16rem;
		  line-height: 0.16rem;
		  position: absolute;
		  top: 50%;
		  margin-top: -0.08rem;
		  right: 0.12rem;
		}
		&:empty:before {
		  content: attr(placeholder);
		  color: @text-tip-color;
		}
	}
}
.qq_hm_tips{
	padding: 0.24rem 0.2rem;
	text-align: center;
	font-size: @font-base-size;
	line-height: 0.2rem;
	color: @t-color-gray;

	.txt{
		font-size: 0.13rem;
		line-height: 0.18rem;
		margin-bottom: 0.16rem;
		text-align: left;
	}
}
.s_form_tips{
	font-size: @font-base-size;
	line-height: 0.2rem;
	color: @t-color-gray;
	margin-top: 0.16rem;
}
.acct_nodata{
	padding-bottom: 0;
	margin-bottom: 0.28rem;

	.icon{
		margin-bottom: 0.16rem;
	}
	h5{
		color: @t-color-normal;
		margin-bottom: 0.08rem;
	}
}
.cm_btn_wrap{
	margin-top: 0.16rem;
	text-align: center;

	.com_btn{
		padding: 0.07rem 0.16rem;
		min-width: 1.2rem;
	}
}
.sub_txt{
	background: #F2F4F8;
	padding: 0.16rem;
	font-size: 0.13rem;
	line-height: 0.18rem;
	color: @t-color-gray;
	margin: 0.16rem 0;

	h5{
		font-size: 0.13rem;
		font-weight: 500;
		color: @t-color-normal;
		margin-bottom: 0.08rem;
	}
	p{
		display: flex;

		.num{
			white-space: nowrap;
		}
	}
}
/*-- add 202404516 end --*/

/*-- add 20240517 start --*/
.pro_sl_item{
	.acct_s_tag.abnormal{
		border-color: @error-color;
		color: @error-color;
	}
	.cont{
		border-top: 1px solid @border-color;
	}
}
/*-- add 20240517 end --*/
/*-- add 20240705 start --*/
.test_numbox span.error {
  color: transparent;
  background: rgba(255, 40, 64, 0.1);
  font-size: 0;
  line-height: 0;
}
.test_numbox span.error:after {
  content: "\e61e";
  width: 0.16rem;
  height: 0.16rem;
  font-size: 0.16rem;
  line-height: 1;
  text-align: center;
  font-family: "wt-iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ffffff;
  border-radius: 50%;
  color: #FF8533;
  position: absolute;
  right: auto;
  bottom: auto;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  z-index: 10;
}
/*-- add 20240705 end --*/
/*-- add 20240722 start --*/
.bus_infobox .title i:before{
	background: @main-color-50;
}
.cond_list li{
	.tit{
		h5{
			margin-bottom: 0.08rem;
		}
		p{
			color: @t-color-gray;
			margin-top: 0;
		}
	}

	.btn{
		display: inline-block;
		line-height: 0.26rem;
		vertical-align: top;
		position: static;
		background: none;
		border: 1px solid @button-bg-1;
	}
	.btn_wrap{
		margin-top: 0.12rem;
		margin-left: 0.32rem;
	}
	&.warn .tit h5:before{
		content: "\e61e";
		color: #CCCCCC;
	}
}
.appro_ic_ok,
.appro_ic_error {
	&:before{
		display: none;
	}
}
.appro_ic_ok{
	background: url(../images/result_suc.png) no-repeat center;
	background-size: 100%;
}
.appro_ic_error{
	background: url(../images/result_fail.png) no-repeat center;
	background-size: 100%;
}
.appro_page .appro_tips{
	margin-top: 0.13rem;
	padding-top: 0.13rem;
}
.appro_page .appro_tips .imp{
	color: @t-color-normal;
	font-weight: 500;
}
.rule_check.no_check{
	padding-left: 0.16rem;
}
.p_button.border{
	background: none;
	border: 1px solid @button-bg-1;
	height: 0.42rem;
	line-height: 2.625;

	&.disabled {
	  background: none !important;
	  border: 1px solid @button-bg-1 !important;
	  color: @button-bg-1 !important;
	  opacity: 0.3;
	}
}
.protocol_list li a:after{
	display: none;
}
.protocol_list li .state{
	right: 0;
}
.p_button {
  background: linear-gradient(90deg, @button-bg-1 0%, @button-bg-2 100%);
  color: #ffffff;
  &.disabled {
    background: linear-gradient(90deg, @button-bg-1 0%, @button-bg-2 100%)  !important;
    color: #ffffff !important;
	opacity: 0.3;
  }
}
.test_numbox{
	background: #F8F8F8;
}
.test_box{
	.title{
		.txt_icon{
			display: inline-block;
			vertical-align: top;
			width: 0.2rem;
			height: 0.24rem;
			background: url(../images/txt_icon.png) no-repeat center;
			background-size: 100% auto;
			margin-left: 0.04rem;
		}
	}
}
.dialog_btn{
	&.block{
		display: block;

		a{
			border-top: 1px solid @border-color;
			border-left: 0 none;

			&:first-child{
				border-top: 0 none;
			}
		}

	}
}
.dialog_cont > div{
	font-size: @font-form-size;
}
.assist_upload_wrap{
	padding-bottom: 0.28rem;
}
.upload_item .pic img{
	width: 100% !important;
	height: 100% !important;
	top: 0 !important;
	left: 0 !important;
}
.upload_com_box.spel .upload_wrap{
	padding-top: 0.2rem;
	padding-bottom: 0.2rem;
}
.upload_com_box.spel .upload_wrap + .photo_tips{
	padding-top: 0.08rem;
}
.watermark{
	top: 0;
	right: 0;
}
.upload_com_box.spel .upload_item .reset_btn{
	background: none;
	border: 1px solid @button-bg-1;

	&:before{
		display: none;
	}
}

.date_input .line{
	margin: 0 0.03rem;
}

.help_txtbox:first-child{
	padding-top: 0.2rem;
}
.upload_item_assist{
	.pic{
		background: #F1F6FD;
		padding-top: 67.5%;

		img{
			display: block;
			width: calc(100% - 0.24rem);
			height: calc(100% - 0.24rem);
			position: absolute;
			top: 0.12rem;
			left: 0.12rem;
			-webkit-transform: translateY(0);
			transform: translateY(0);
		}
	}
	.btn{
		position: static;
		padding-top: 0;
		height: auto;
		position: relative;
		margin-top: 0.08rem;
		display: block;
		text-align: center;
		font-size: 0.14rem;
		border-radius: 0.02rem;
		line-height: 2.42;
		background: @button-bg-1;
		color: #ffffff;


		&:before{
			display: none;
		}
	}
	.reset_btn {
		padding: 0;
		height: auto;
		position: static;
		left: 0;
		bottom: 0;
		-webkit-transform: translateX(0);
		transform: translateX(0);
		margin-top: 0.08rem;
		display: block;
		text-align: center;
		font-size: 0.14rem;
		border-radius: 0.02rem;
		line-height: 2.42;
		background: none;
		border: 1px solid @button-bg-1;
		color: @button-bg-1;

		&:before{
			display: none;
		}
	}
	.watermark{
		width: 0.32rem;
		height: 0.32rem;
		top: 0.12rem;
		right: 0.12rem;
	}
}
.footer{
	.com_tips{
		border-top: 0 none;
		text-align: center;
		padding-bottom: 0.1rem;
	}

}
.bc_text p .readed{
	color: @main-color;
}
.progress_chart .bg svg{
	fill: @main-color;
}
.result_tips .icon{
	margin-bottom: 0.2rem;

	&:before{
		display: none !important;
	}
	&.ing{
		background: url(../images/result_ing.png) no-repeat center;
		background-size: 100%;
	}
	&.ok{
		background: url(../images/result_suc.png) no-repeat center;
		background-size: 100%;
	}
	&.fail{
		background: url(../images/result_fail.png) no-repeat center;
		background-size: 100%;
	}
	&.reject{
		background: url(../images/result_reject.png) no-repeat center;
		background-size: 100%;
	}
	&.vedio_ok{
		background: url(../images/result_suc.png) no-repeat center;
		background-size: 100%;
	}
	&.vedio_error{
		background: url(../images/result_fail.png) no-repeat center;
		background-size: 100%;
	}
}
.lz_tipbox .title,
.lz_tipbox ul li span{
	color: @t-color-gray;
}
.fc_basebox .pic{
	margin-top: 0.2rem;
}
.video_info ul li i{
	background-color: #BBBBBB;
}
.result_tips{
	padding-left: 0.5rem;
	padding-right: 0.5rem;
}
.result_advbox{
	margin: 0.24rem 0.16rem;

	img{
		display: block;
		width: 100%;
	}
}
.video_loading{
	color: #ffffff;
}
.bc_wrapbox{
	position: relative;
}
.inte_bc_img {
	width: 0.36rem;
	height: 0.36rem;
	background: url(../images/bc_ic_bg.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.15rem;
	left: 0.15rem;

	img {
		width: 0.26rem;
		height: 0.2rem;
		position: absolute;
		top: 50%;
		left: 50%;
		margin: -0.1rem 0 0 -0.13rem;
	}
}

.inte_bc_cont {
	height: 0.84rem;
	overflow: hidden;
	position: relative;
	margin-left: 0.5rem;
	margin-top: 0.03rem;
	margin-right: -0.15rem;

	.bc_text {
		width: 100%;
		min-height: 0.84rem;
		position: absolute;
		bottom: 0;
		left: 0;

		p {
			font-size: 0.22rem;
			line-height: 0.28rem;
		}
		.ing {
			color: rgba(255, 255, 255, 0.7);
		}
	}
}
.bc_rate {
	height: 0.04rem;
	border-radius: 0.04rem;
	background: rgba(204, 204, 204, 0.5);
	position: relative;
	margin: 0 0.4rem;

	b {
		display: block;
		height: 0.04rem;
		border-radius: 0.04rem;
		background: @main-color;
	}
}
.answer_text {
	text-align: left;
	margin-top: 0;
	font-size: 0.22rem;
	line-height: 0.28rem;

	.ok,
	.error {
		position: relative;
		padding-right: 0.28rem;

		&:after {
			content: "";
			width: 0.22rem;
			height: 0.22rem;
			border-radius: 50%;
			background: #ffffff;
			position: absolute;
			top: 50%;
			margin-top: -0.11rem;
			right: 0;
			font-size: 0.22rem;
			line-height: 1;
			font-family: "wt-iconfont" !important;
			font-style: normal;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			z-index: 10;
		}
	}
	.ok {
		color: @suc-color;

		&:after {
			content: "\e621";
			color: @suc-color;
		}
	}
	.error {
		color: @error-color;

		&:after {
			content: "\e61e";
			color: @error-color;
		}
	}
}
.review_video .btn{
	width: auto;
	left: 50%;
	margin-left: 0;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);
}
.view_loading{
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 100%;
	text-align: center;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #ffffff;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);

	.icon{
		display: block;
		width: 0.38rem;
		height: 0.38rem;
		margin: 0 auto 0.12rem;
		background: url(../images/view_loading.png) no-repeat center;
		background-size: 100%;
		-webkit-animation: allrotate 1.2s infinite linear;
		animation: allrotate 1.2s infinite linear;
	}
}
.v_control_box{
	background: rgba(0, 0, 0, 0.4);
	text-align: center;
	padding: 0.12rem 0.1rem 0.25rem;
	color: #ffffff;
	text-align: left;
	width: 100%;
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 50;
}
.v_control_info{
	display: flex;
	height: 0.24rem;
	align-items: center;

	.v_control_time{
		font-size: 0.14rem;
		line-height: 0.2rem;
		flex: 1;
		min-width: 0;
	}
}
.v_control_opea{
	height: 0.24rem;
	position: relative;
	font-size: 0;

	.item{
		margin-left: 0.2rem;
		height: 0.24rem;
		display: inline-block;
		vertical-align: top;
		position: relative;

		&:first-child{
			margin-left: 0;
		}
	}
}
.v_btn_speed{
	height: 0.24rem;
	line-height: 0.24rem;
	font-size: 0.14rem;
	color: #ffffff;
}
.v_fast_prev,
.v_fast_next,
.v_full_screen{
	display: block;
	width: 0.24rem;
	height: 0.24rem;
}
.v_fast_prev{
	background: url(../images/v_fast_prev.png) no-repeat center;
	background-size: 100%;
}
.v_fast_next{
	background: url(../images/v_fast_next.png) no-repeat center;
	background-size: 100%;
}
.v_full_screen{
	background: url(../images/v_full_screen.png) no-repeat center;
	background-size: 100%;
}
.v_control_track{
	height: 2px;
	background: rgba(255, 255, 255, 0.25);
	border-radius: 2px;
	margin-top: 0.14rem;
	position: relative;

	.track_bar{
		height: 2px;
		border-radius: 2px;
		background: rgba(255, 255, 255, 0.25);
		position: relative;

		.slider{
			width: 12px;
			height: 12px;
			border-radius: 50%;
			background: #FFFFFF;
			position: absolute;
			top: -5px;
			right: -5px;
			z-index: 10;
		}
	}
}
.v_dot_item{
	height: 2px;
	background: @main-color;
	border-radius: 2px;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 5;

	i{
		display: block;
		width: 4px;
		height: 4px;
		background: #ffffff;
		border-radius: 50%;
		position: absolute;
		top: -1px;

		&.begin{
			left: -2px;
		}
		&.end{
			right: -2px;
		}
	}
}
.v_dot_error{
	height: 2px;
	border-radius: 2px;
	background: @error-color;
	opacity: 0.4;
	z-index: 6;
	position: absolute;
	top: 0;
}
.review_video.full_screen{
	width: 100%;
	height: 100%;
	border-radius: 0;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 2000;

	.icon_back{
		color: #ffffff;
	}
}
.speed_ly_wrap{
	width: 1.3rem;
	background: #404040;
	border-radius: 0.06rem;
	position: absolute;
	right: 0;
	bottom: 0.3rem;
	z-index: 50;

	&:before{
		content: '';
		width: 0.1rem;
		height: 0.08rem;
		background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAQCAYAAAAWGF8bAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAC2SURBVDhPrZJREcIwEEQjAQk4SaRUAhLqBAmVgAQkRAISYJdeZkjYcAnTN7MfTfe219yFlNINeh6geyAxxrN4OS3mvAMJHlZlmtDVonYQeMLhozGNKlfdFXB4EWZX/DuL+AaG2QFlK9Xga/DIQin4FyvtA+PWFnZUD6IHLxhmd0D0WYkPL1qFfGisuwICuUa5CSnSa+KBokWEjQ2iBwLaNfq9Jh7oBhlVd8le/Q/vC1oPCZsnhBfAOhtp/gI+ggAAAABJRU5ErkJggg==") no-repeat center;
		background-size: 100%;
		position: absolute;
		bottom: -0.05rem;
		right: 0.1rem;
	}
}
.speed_sele_item{
	font-size: 0.14rem;
	line-height: 0.2rem;
	padding: 0.04rem 0.1rem 0.04rem 0.3rem;
	color: #ffffff;
	position: relative;
	border-top: 1px solid rgba(255, 255, 255, 0.15);

	&:first-child{
		border-top: 0 none;
	}
	&.active:before{
		content: '';
		width: 0.16rem;
		height: 0.16rem;
		background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADiSURBVFhH7ZbBDcIwDEUzQkfoCB2FUdigbACbMAIjMAIjsEHM/81PJUQOHBIfKj8pauNWfqmVWE1BEBweM5swZk19oTjn/ObgQhT2A+IXxBXfKkC+SkwuCvsA4Vy8ZqyCwn5E6Um30iPX3+e4e+mZBEnrOV4UbjKk9Eg0Sb6VFJfmVzHOdwjfU7gPyHkqqfdF/HQ0xSv9dz0EZyXnIh4Kb2Dus+shuknCRVwZw61vw4HkLh+FK6WakvENBxJuymfxfeHXcCDj0dy/nPd65Ae8C8S1P4wvfQuKMfx/MoLgYKT0AcfUfTUSBoxSAAAAAElFTkSuQmCC") no-repeat center;
		background-size: 100%;
		position: absolute;
		top: 50%;
		margin-top: -0.08rem;
		left: 0.1rem;
	}
}
.v_review_tips{
	max-width: 3rem;
	min-width: 2rem;
	background: rgba(0, 0, 0, 0.7);
	padding: 0.14rem 0.2rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #ffffff;
	border-radius: 0.08rem;
	position: absolute;
	bottom: 1rem;
	left: 50%;
	-webkit-transform: translateX(-50%);
	transform: translateX(-50%);
	z-index: 60;
}
.acct_status_item{
	margin: 0 0.16rem 0.08rem;
	border-radius: 0.06rem;
	overflow: hidden;
}
.acct_list li{
	.acct_s_tag.abnormal {
	  border-color: @main-color;
	  color: @main-color;
	}
}
.acct_list li .icon_radio,
.acct_list li .icon_check{
	padding-left: 0;
	padding-right: 0.5rem;

	&:before{
		left: auto;
		right: 0;
	}
	&.disabled{
		color: @t-color-lightgray;

		&:before{
			display: none;
		}
	}
}
.tag_zhu_span{
	display: inline-block;
	vertical-align: top;
	font-style: normal;
	width: 0.18rem;
	height: 0.18rem;
	text-align: center;
	line-height: 0.18rem;
	color: #ffffff;
	font-size: 0.1rem;
	background: @main-color;
	border-radius: 0.02rem;
	margin-left: 0.05rem;
	position: relative;
	top: 0.03rem;
}
.acct_emptybox{
	p{
		em{
			font-size: @font-base-size;
			color: @t-color-lightgray;
		}
	}
	.r_link_arrow{
		color: @button-bg-1;

		&:after{
			color: @border-deep-color;
		}
	}
}

/*-- add 20240722 end --*/
/*-- add 20240814 start --*/
.type_navlist{
	margin: 0 0.16rem 0.16rem;

	li{
		border-radius: 0.06rem;
		border: 1px solid #FFF;
		background: linear-gradient(0deg, rgba(255, 245, 245, 0.00) 38.79%, #FFF5F5 114.97%), #FFF;
		padding: 0.15rem 0.44rem 0.15rem 0.15rem;
		position: relative;
		margin-top: 0.12rem;

		&:first-child{
			margin-top: 0;
		}
		&:after{
		  content: "\e619";
		  width: 0.14rem;
		  height: 0.14rem;
		  text-align: center;
		  font-size: 0.14rem;
		  line-height: 1;
		  color: @t-color-lightgray;
		  font-family: "wt-iconfont" !important;
		  font-style: normal;
		  -webkit-font-smoothing: antialiased;
		  -moz-osx-font-smoothing: grayscale;
		  position: absolute;
		  top: 50%;
		  margin-top: -0.07rem;
		  right: 0.15rem;
		}
		h5{
			font-size: 0.18rem;
			line-height: 1.4;
			font-weight: 500;
			color: @t-color-normal;
			margin-bottom: 0.08rem;
		}
		p{
			font-size: @font-base-size;
			line-height: 1.5;
			color: @t-color-gray;
			text-align: justify;
		}
	}
}
.ellipsis_cont{
	padding: 0 0 0.15rem;
	font-size: 0.12rem;
	line-height: 0.18rem;
	color: @t-color-gray;

	.txt{
		&.ellipsis{
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}
	}
	.com_link{
		display: inline-block;
		vertical-align: top;
	}
}
.sub_gl_item{
	padding-left: 0.23rem;
	position: relative;

	&:before{
		content: '';
		box-sizing: border-box;
		width: 0.12rem;
		height: 50%;
		border-left: 1px solid #C5CAD4;
		border-bottom: 1px solid #C5CAD4;
		border-bottom-left-radius: 0.04rem;
		position: absolute;
		top: 0;
		left: 0.05rem;
	}
}
.acct_status_item{
	.tit{
		.switch{
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 0;
			z-index: 50;
		}
	}
}
.acct_txt_tips{
	font-size: @font-base-size;
	line-height: 1.5;
	color: @t-color-gray;
	padding: 0.16rem;
}
.switch{
	width: 0.4rem;
}
.switch > .switch-inner > .switch-arrow{
	width: 0.2rem;
	height: 0.2rem;
	top: 0.02rem;
	right: 0.18rem;
}
.switch > input[type='checkbox']:checked + .switch-inner .switch-arrow{
	right: 0.02rem;
}
.switch > input[type='checkbox']:disabled + .switch-inner{
	opacity: 0.5;
}
.qx_exp_box{
	margin: 0 -0.14rem;
	padding: 0 0.14rem;
	text-align: left !important;
	font-size: @font-form-size;
	line-height: 1.5;
	color: @t-color-gray !important;

	.tit{
		font-size: @font-form-size;
		font-weight: 500;
		color: @t-color-normal;
		margin: 0.05rem 0;
	}

	.list{
		margin-left: 0.08rem;

		li{
			padding-left: 0.16rem;
			position: relative;

			&:before{
				content: '';
				width: 0.06rem;
				height: 0.06rem;
				background: @t-color-gray;
				border-radius: 50%;
				position: absolute;
				top: 0.09rem;
				left: 0;
			}
		}
	}
}
.pb16{
	padding-bottom: 0.16rem;
}
.cm_sele_list.right{
	li{
		.layout{
			flex-direction: row-reverse;

			.icon_check,
			.icon_radio{
				margin-right: 0;
				margin-left: 0.06rem;
			}
			p{
				flex: 1;
				min-width: 0;
			}
		}
	}
}

/*-- add 20240814 end --*/
/*-- add 20240905 start --*/
.p_button{
  transition: all 0.3s;
  &:active{
          background: linear-gradient(90deg, #CC2033 0%, #CC2033 100%);
  }
  &.border:active{
          background: #FFF0F0;
  }
}
.cond_list{
	li{
		.btn_wrap{
			font-size: 0;
			.btn{
				margin-left: 0.08rem;
				&:first-child{
					margin-left: 0;
				}
			}
		}
	}
}
/*-- add 20240905 end --*/
/*-- add 20240912 start --*/
.tip_txtbox{
	color: @t-color-gray;
	background: none;
}
.acct_list_tips{
  color: @t-color-gray;
	background: none;
}
.result_tips{
  color: @t-color-gray;
	background: none;
}
.result_bottom_tips{
  color: @t-color-gray;
	background: none;
}
.reject_txtinfo {
  .title {
    color: @t-color-gray;
    background: none;
  }
}
.imp_c_tips {
  p.warn {
    .imp {
      color: @t-color-gray;
    background: none;
    }
  }
}
/*-- add 20240912 end --*/

/*-- add 20241007 start --*/
.acct_status_item{
	margin-top: 0.08rem;
}
.com_title + .acct_status_item{
	margin-top: 0;
}
.acct_list li{
	.imp_c_tips{
		margin-left: 0;
		margin-right: 0;
		border-top: 0 none;
		padding-top: 0;

		p{
			font-size: 0.14rem;
			line-height: 1.42;
			padding: 0;
		}
	}
}
.acct_status_item{
	.xh_tit{
		margin: 0 0.16rem;
		position: relative;
		font-size: @font-form-size;
		line-height: 1.5;
		font-weight: 500;
		border-bottom: 1px solid @border-color;

		h5{
			padding: 0.16rem 0.6rem 0.16rem 0;
			font-size: @font-sub-title-size;
			line-height: 1.5;
			font-weight: 500;
			color: @t-color-normal;
		}
		.wrap{
			display: flex;
			align-items: center;

			h5{
				flex: 1;
				min-width: 0;
			}
			.icon_check{
				padding: 0;
				width: 0.18rem;
				height: 0.18rem;
			}
		}
		.imp_c_tips{
			margin-left: 0;
			margin-right: 0;
			border-top: 0 none;
			padding-top: 0;
			margin-top: -0.06rem;

			p{
				font-size: 0.14rem;
				line-height: 1.42;
				padding: 0;
			}
		}
	}
}
.xh_switchlist{
	display: flex;
	margin: 0.06rem -0.06rem 0;
	padding-bottom: 0.1rem;

	.item{
		width: 50%;
		padding: 0 0.06rem 0.06rem;

		span{
			display: block;
			border: 1px solid #DDDDDD;
			border-radius: 0.04rem;
			line-height: 0.2rem;
			padding: 0.07rem 0.12rem;
			text-align: center;
			font-size: @font-base-size;
			color: @t-color-normal;
			position: relative;
			overflow: hidden;
		}
		&.active{
			span{
				border-color: @main-color;
				color: @main-color;
				background: @main-color-3;

				&:before{
					content: '';
					width: 0.12rem;
					height: 0.1rem;
					border-radius: 0 0 0 0.04rem;
					background: @main-color url(../images/icon_check02.png) no-repeat center;
					background-size: 0.12rem;
					position: absolute;
					top: -1px;
					right: -1px;
				}
			}
		}
	}
}
.xh_act_title{
	background: #ffffff;
	padding: 0.16rem;
	font-size: @font-form-size;
	line-height: 1.5;
}
.xh_pwrod_item{
	margin-bottom: 0.08rem;
	background: #ffffff;
	padding: 0 0.16rem;

	.tit{
		padding: 0.16rem 0;
		font-size: @font-form-size;
		line-height: 1.5;
		border-bottom: 1px solid @border-color;
	}
}
.acct_list li .txt_p{
	position: relative;
}
.ztg_check{
	display: inline-block;
	vertical-align: top;
	border: 1px solid #DDDDDD;
	border-radius: 0.04rem;
	padding: 0.07rem 0.11rem;
	font-size: @font-base-size;
	line-height: 0.2rem;
	color: @t-color-normal;
	position: absolute;
	top: 50%;
	margin-top: -0.18rem;
	right: 0;
	z-index: 50;

	&.active{
		border-color: @main-color;
		color: @main-color;
		background: @main-color-3;

		&:before{
			content: '';
			width: 0.12rem;
			height: 0.1rem;
			border-radius: 0 0 0 0.04rem;
			background: @main-color url(../images/icon_check02.png) no-repeat center;
			background-size: 0.12rem;
			position: absolute;
			top: -1px;
			right: -1px;
		}
	}
}
.xh_erro_cont{
	padding-bottom: 0.16rem;

	.item{
		font-size: @font-base-size;
		line-height: 0.2rem;
		margin-top: 0.08rem;
		color: @t-color-gray;
		position: relative;
		padding-left: 0.24rem;

		&:first-child{
			margin-top: 0;
		}
		&:before{
			content: "\e61e";
			width: 0.16rem;
			height: 0.16rem;
			font-size: 0.16rem;
			line-height: 1;
			color: #D9D9D9;
			font-family: "wt-iconfont" !important;
			font-style: normal;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			position: absolute;
			top: 0.02rem;
			left: 0;
		}
		&.warn{
			color: @error-color;

			&:before{
				color: @error-color;
			}
		}
	}
}
.ztg_input{
	position: relative;
	background: #F7F8FB;
	margin-top: 1px;
	padding: 0 0.16rem;
	display: flex;

	&:last-child{
		border-bottom-left-radius: 0.04rem;
		border-bottom-right-radius: 0.04rem;
	}
	&:first-child{
		border-top-left-radius: 0.04rem;
		border-top-right-radius: 0.04rem;
	}
	.label{
		padding: 0.12rem 0;
		font-size: @font-form-size;
		line-height: 1.5;
		width: 0.84rem;
	}
	.t1{
		flex: 1;
		min-width: 0;
		width: 100%;
		height: 0.48rem;
		border: 0 none;
		background: none;
		font-size: @font-form-size;
		outline: none;
		color: @t-color-normal;
		text-align: right;
	}
}
.ztg_btn{
	padding: 0.16rem;
	text-align: center;
}
.acct_list.xh li{
	.icon_radio.disabled,
	.icon_check.disabled{
		color: @t-color-normal;

		&:before{
			display: block;
			color: #BBBBBB;
		}
	}
}
.xh_switchlist{
	.item{
		&.active{
			span{
				&:before{
					content: "\e61f";
					color: #ffffff;
					font-size: 0.12rem;
					line-height: 0.1rem;
					text-align: center;
					background: @main-color;
					font-family: "wt-iconfont" !important;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
				}
			}
		}
		&.disabled{
			span{
				background-color: rgba(221, 221, 221, 0.50);
				border-color: #DDDDDD;
				color: @t-color-lightgray;

				&:before{
					background-color: #DDDDDD;
					color: #BBBBBB;
				}
			}
		}
	}
}

/*-- add 20241007 end --*/

/*-- add 20241025 start --*/
.market_ctlist li{
	.base{
		p{
			position: relative;
			padding: 0.02rem 0;


			.com_btn{
				position: absolute;
				top: -0.02rem;
				right: 0;
				z-index: 50;
			}
		}
	}
}
.market_formlink{
	margin: 0.08rem 0.16rem;
	background: #ffffff;
	border-radius: 0.06rem;
	padding: 0.16rem;
	display: flex;
	align-items: center;
	font-size: @font-form-size;
	line-height: 0.24rem;

	p{
		flex: 1;
		min-width: 0;
	}
	.time{
		font-size: @font-base-size;
		color: @t-color-lightgray;
		margin-left: 0.16rem;
	}
	.drop_arrow{
		margin-left: 0.1rem;
		position: relative;
	}
}
.no-wrap {
  white-space: nowrap;
}
/*-- add 20241025 end --*/

/*-- add 20250616 start --*/
.qq_hm_page{
	background: linear-gradient(128deg, #FFCDCD 8.63%, #FFF 38.54%, #FFFBFB 54.4%, #F3F4F6 66.04%);
}
.s_input_item{
	align-items: flex-start;

	.tit{
		display: flex;
		align-items: center;
		height: 0.48rem;
	}
	.yyb_info_text{
		padding-left: 0.12rem;
		color: @t-color-lightgray;
		padding-bottom: 0.06rem;

		p{
			padding-left: 0;
			margin-top: 0;
		}
	}
}
.select_module{
	padding: 0 0.16rem 0.04rem;

	.title{
		padding: 0.07rem 0;
		font-size: @font-base-size;
		line-height: 0.2rem;
		font-weight: normal;
		color: @t-color-normal;
	}
}
.select_list_2{
	display: flex;
	flex-wrap: wrap;
	margin: 0 -0.04rem;

	li{
		min-width: 33.333333%;
		padding: 0 0.04rem 0.08rem;

		span{
			display: block;
			background: #F5F5F5;
			border-radius: 0.5rem;
			font-size: @font-base-size;
			line-height: 0.2rem;
			padding: 0.06rem 0.05rem;
			color: @t-color-normal;
			text-align: center;

			&.local{
				color: @main-color;
				background: #FFF3F3;
			}
		}
		&.active{
			span{
				color: #ffffff;
				background: @main-color-3;

				&.lacal{
					color: #ffffff;
					background: @main-color-3;
				}
			}
			.now_local_icon{
				background-image: url(../images/now_local_white.png);
			}
		}
	}
}
.now_local_icon{
	display: inline-block;
	vertical-align: top;
	width: 0.18rem;
	height: 0.2rem;
	background: url(../images/now_local.png) no-repeat center;
	background-size: 100% auto;
	margin-right: 0.05rem;
}
.sele_lyinfo{
	span{
		border-radius: 0.04rem;
		font-size: 0.15rem;
		padding-left: 0.1rem;
		padding-right: 0.1rem;

		&.off{
			padding-right: 0.18rem;
		}
	}
}
.network_list{
	li{
		.tips{
			color: #FF7015 !important;
		}
	}
}
.black_col{
	color: @t-color-normal !important;
}
.qq_bannerbox{
	position: relative;

	&.spel{
		margin-bottom: -20%;
	}

	.txt{
		font-size: 0.13rem;
		line-height: 0.18rem;
		color: @t-color-gray;
		position: absolute;
		bottom: 0;
		margin-bottom: 27%;
		left: 0.15rem;
		right: 37%;
		z-index: 5;
	}
}
@font-face {
	font-family: 'D-DIN';
	src: url("../fonts/D-DIN.ttf");
	font-weight: normal;
	font-style: normal;
}
.cm_qq_wrap{
	position: relative;
	z-index: 50;
	padding: 0 0.15rem 0.15rem;
}
.cm_qq_module{
	position: relative;
	margin-top: 0.45rem;
	margin-bottom: 0.15rem;
	z-index: 10;

	&:first-child{
		margin-top: 0.28rem;
	}
	.step{
		width: 0.9rem;
		height: 0.4rem;
		padding-bottom: 0.12rem;
		padding-right: 0.15rem;
		text-align: center;
		font-family: 'D-DIN';
		font-size: 0.16rem;
		line-height: 0.28rem;
		font-weight: 700;
		color: #FFF6F4;
		background: url(../images/cm_qq_modtit.png) no-repeat left top;
		background-size: 100% auto;
		position: absolute;
		top: -0.28rem;
		left: 0;
		z-index: -1;
	}
	.wrap{
		overflow: hidden;
		background: #ffffff url(../images/cm_qq_modbg.png) no-repeat center top;
		background-size: 100% auto;
		border: 1px solid #ffffff;
		border-radius: 0.12rem;
		padding: 0.27rem 0.12rem 0.12rem;
	}
	.title{
		text-align: center;
		margin-bottom: 0.14rem;

		h3{
			font-size: 0.24rem;
			line-height: 0.34rem;
			font-weight: 500;

			.imp{
				color: @main-color;
			}
		}
		p{
			font-size: @font-base-size;
			line-height: 0.2rem;
			margin-top: 0.05rem;
		}
	}
	.ce_btn{
		padding: 0;
		margin: 0.14rem 0;
		display: block;

		.p_button{
			width: 1.6rem;
			margin: 0 auto;
			height: 0.36rem;
			line-height: 0.36rem;
			box-shadow: 0 0.07rem 0.1rem 0 rgba(255, 56, 77, 0.3);
		}
	}
	.n_list{
		margin: 0.24rem 0 0.12rem;
		background: #F2F4F8;
		border-radius: 0.08rem;

		li{
			border-top: 1px solid #ffffff;
			display: flex;
			padding: 0.1rem 0.12rem;
			font-size: @font-base-size;
			line-height: 0.24rem;

			.tit{
				width: 1rem;
			}
			p{
				flex: 1;
				min-width: 0;
				color: @t-color-lightgray;
			}
			.link{
				color: @link-color;
				display: inline-block;
				vertical-align: top;
				padding-right: 0.16rem;
				position: relative;

				&:before{
					content: "\e619";
					font-family: "wt-iconfont" !important;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					color: @link-color;
					width: 0.12rem;
					height: 0.12rem;
					font-size: 0.12rem;
					line-height: 0.12rem;
					position: absolute;
					top: 50%;
					margin-top: -0.06rem;
					right: 0;
				}
			}
		}
	}
	.n_tips{
		font-size: @font-base-size;
		line-height: 0.2rem;
		color: @t-color-lightgray;
		margin-top: 0.12rem;
	}
}
.cm_qq_tips{
	font-size: @font-base-size;
	line-height: 0.2rem;
	color: @t-color-gray;
	margin: 0.12rem 0;

	p{
		margin-top: 0.06rem;
	}
}
.qq_adv_img{
	margin: 0.2rem 0 0.1rem;

	img{
		display: block;
		width: 100%;
		border-radius: 0.08rem;
	}
}
/*-- add 20250616 end --*/
