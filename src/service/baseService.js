import { Request, md5Encrypt, SmEncrypt } from '@thinkive/axios';
import intercept from 'netIntercept';

const config = window.$hvue.config;
let fetch = new Request({ intercept, config });
console.log('创建Request实例');

// 开发环境日志
const isDev =
  process.env.NODE_ENV === 'development' ||
  window.location.hostname === 'localhost' ||
  window.location.hostname.includes('uatfzsdbusinesscdn.yjbtest.com');

function logApiResponse(url, params, response) {
  if (!isDev) return;

  console.log(`API: ${url}`);
  console.log('请求:', params);
  console.log('响应:', response);
}

export function baseService({ url, params, options = {} }) {
  let _params = {};
  if (!options.private) {
    let opSource = '';
    let opStation = ''; // 站点信息登录后从网关获取
    // let opStation = getOpStation();
    let { android, ios, pc } = $hvue.iBrowser;
    let platform = $hvue.platform;
    if (platform === '0') {
      opSource = '4';
    } else {
      if (android) {
        opSource = '1';
      }
      if (ios) {
        opSource = '2';
      }
      if (pc) {
        opSource = '3';
      }
    }
    _params = {
      opEntrustWay: $hvue.customConfig.opEntrustWay, //TODO 联调测试
      merchantId: $hvue.customConfig.merchantId,
      opSource,
      // source: opSource,
      opStation
    };
  }
  let paramMD5;
  if (options.cache === true) {
    // 结果是否缓存到session
    paramMD5 = md5Encrypt(JSON.stringify(Object.assign(_params, params)));
    let res = $h.getSession(paramMD5);
    if (res) {
      if (res.code === 0) {
        return Promise.resolve(res);
      }
    }
  }
  console.log('~~~~serviceToken~~~~~===' + $h.getSession('authorization'));
  let _options = {
    // 默认的请求设置
    url: url,
    sign: 'sm', // 是否加签
    signModule: 'signSm', // 签名参数模块（对象）
    encode: false, // 是否默认编码
    method: 'POST', // 请求方法
    loading: false, // 是否显示loading
    requestKey: Math.random().toString(36).slice(-8), // 随机标识用于清空当前请求
    headers: {
      'tk-encrypt-response': !isPathAllowed(url),
      Accept:
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
      'Content-Type': 'application/json',
      Authorization: $h.getSession('authorization'),
      'tk-token-authorization': $h.getSession('authorization'),
      'tk-jwt-authorization': $h.getSession('jwtToken'),
      'tk-flow-token': sessionStorage.getItem('TKFlowToken'),
      'x-bus-id': $h.getSession('bizType'),
      'tk-two-factor-token': $h.getSession('tkTwoFactorToken'),
      merchantId: $hvue.customConfig.merchantId,
      isLoading: true // 是否显示loading (此属性非基础提供，自定义化实现所需)
    }
  };

  params = Object.assign(_params, params);
  options = Object.assign(_options, options);

  return new Promise((resolve, reject) => {
    fetch({
      params,
      options
    }).then(
      (res) => {
        // 记录响应日志
        logApiResponse(url, params, res);

        if (options.cache === true) {
          $h.setSession(paramMD5, res);
        }
        if (!options.filter && options.responseType !== 'blob') {
          if (res.code === 0) {
            resolve(res);
          } else {
            reject(res.msg);
          }
        } else {
          resolve(res);
        }
      },
      (err) => {
        reject(err);
      }
    );
  });
}

function isPathAllowed(fullPath) {
  try {
    // 解析完整URL，获取路径部分（兼容带协议、域名、端口、查询参数的情况）
    const url = new URL(fullPath);
    const pathname = url.pathname;

    // 分割路径为 segments（去除空字符串，例如 ["", "bc-bff-server", "captcha"]）
    const segments = pathname.split('/').filter((segment) => segment);

    if (segments.length < 2) {
      // 路径格式异常（至少需要 "工程名/路径" 结构）
      return false;
    }

    // 提取工程名后的核心路径（从 segments[1] 开始拼接，忽略工程名）
    const targetPath = segments.slice(1).join('/');

    // 遍历配置规则匹配核心路径
    return config.excludeEncryptResponsePath.some((apiPath) => {
      if (apiPath instanceof RegExp) {
        // 正则规则：直接测试路径
        return apiPath.test(targetPath);
      } else {
        // 字符串规则：精确匹配
        return targetPath === apiPath;
      }
    });
  } catch (error) {
    // URL解析失败（如非法URL格式），返回false
    console.error('Invalid URL format:', error);
    return false;
  }
}
