const config = {
  sm4Key: '220c5ec9f3c013a5', // 加密秘钥
  decSm4Key: 'thinkivethinkivethinkivethinkive', // 解密密钥
  sm4KeyCbc: '9f7ba3f144954187964142ba00aa2bcc', // Cbc加密秘钥
  sm2Key: '25AD8C6B1E5479F09B6D504E43365F0889DFA4D8AECBAA857B4D646AA3B44E02', //国密加密私钥 统一登录用
  // 国密加密
  signSm: {
    secret: 'A/ls0u0hUu+dvUYyH85LDmCQ2KHteLW0wzlB94bntjNt6GXWtNv3AOxrprueUOFX',
    merchant_key: 'thinkive',
    subject_key: 'default',
    encrypted: true
  },
  noCachePage: [
    'login',
    'home',
    'introduce',
    'lrIntroduce',
    'businessIntroduce',
    'questionVisitList',
    'fundAccountPhoneConfirm',
    'bcPreBizList',
    'idVerify'
  ], // 需要清空缓存页面
  notKeepAliveArr: ['transferPage'], // 不需要缓存的页面组件 不管第几次进去都会执行created方法
  noSsoLoginList: [
    'fundAccountConfirm',
    'idConfirm',
    'pdfDownload',
    'idVerify'
  ], // 不需要做统一登录的页面
  noFLowInitPage: [
    'introduce',
    'lrIntroduce',
    'businessIntroduce',
    'doubleRecord',
    'doubleRecordHistory',
    'proofApply'
  ], // 不需要自动流程初始化的页面路由

  sessionSaveToApp: true,
  sessionKeyToApp: ['authorization', 'userInfo'],
  excludeEncryptResponsePath: [
    'captcha',
    'client/ocrParseIDCard',
    'client/ocrParseBankCard',
    'media/uploadWithMultipartFile',
    'media/imageUpload',
    /^file\/.*$/,
    /^common\/.*$/,
    'video/upload',
    'download/previewImage',
    'business/adQry',
    'download/previewImages',
    'business/portalAnnouncementQry',
    'business/queryPortalList',
    'business/queryBusinessIntroduce',
    'business/businessProcessNumQry',
    'business/accPermissionViewPageQry',
    'agree/queryAgreementPdf'
  ] //无须进行响应加密的接口
};

window.$hvue = { config };
export default config;
